import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../utils/app_theme.dart';
import '../services/premium_access_service.dart';
import '../services/admob_service.dart';

class PremiumAccessDialog extends StatefulWidget {
  final String title;
  final String description;
  final VoidCallback? onPremiumGranted;
  final bool isPremiumContent;

  const PremiumAccessDialog({
    super.key,
    required this.title,
    required this.description,
    this.onPremiumGranted,
    this.isPremiumContent = true,
  });

  @override
  State<PremiumAccessDialog> createState() => _PremiumAccessDialogState();
}

class _PremiumAccessDialogState extends State<PremiumAccessDialog> {
  bool _isLoading = false;
  Map<String, dynamic>? _premiumStatus;

  @override
  void initState() {
    super.initState();
    _loadPremiumStatus();
  }

  Future<void> _loadPremiumStatus() async {
    final status = await PremiumAccessService.getPremiumStatus();
    setState(() {
      _premiumStatus = status;
    });
  }

  Future<void> _watchAdForPremium() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Check if rewarded ad is available
      final adMobService = AdMobService();

      if (kDebugMode) {
        print(
          '🎯 Premium Ad Debug: isRewardedAdLoaded=${adMobService.isRewardedAdLoaded}',
        );
        print(
          '🎯 Premium Ad Debug: rewardedAd object=${adMobService.rewardedAd != null}',
        );
      }

      if (!adMobService.isRewardedAdLoaded) {
        if (mounted) {
          _showErrorDialog(
            'Ad Not Available',
            'Rewarded ads are currently loading. Please wait a moment and try again.\n\nTip: Make sure you have a stable internet connection.',
          );
        }
        return;
      }

      final success = await PremiumAccessService.watchAdForPremiumAccess();

      if (success) {
        if (mounted) {
          Navigator.of(context).pop();
          _showSuccessDialog(
            'Premium Access Granted!',
            'You now have 24 hours of premium access to all wallpapers.',
          );
          widget.onPremiumGranted?.call();
        }
      } else {
        if (mounted) {
          _showErrorDialog(
            'Ad Not Available',
            'The ad could not be shown. This might happen if:\n\n• No ads are available right now\n• You\'ve reached the daily ad limit\n• There\'s a network issue\n\nPlease try again in a few minutes.',
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('🎯 Error in _watchAdForPremium: $e');
      }
      if (mounted) {
        _showErrorDialog(
          'Error',
          'Failed to load ad. Please check your internet connection and try again later.',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _watchAdForAdFree() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Check if rewarded ad is available
      final adMobService = AdMobService();
      if (!adMobService.isRewardedAdLoaded) {
        // Try to load a new ad
        await adMobService.loadRewardedAd();

        // Wait a bit for the ad to load
        await Future.delayed(const Duration(seconds: 3));

        if (!adMobService.isRewardedAdLoaded) {
          if (mounted) {
            _showErrorDialog(
              'Ad Not Available',
              'No ads are currently available. Please check your internet connection and try again later.',
            );
          }
          return;
        }
      }

      final success = await PremiumAccessService.watchAdForAdFreeAccess();

      if (success) {
        if (mounted) {
          Navigator.of(context).pop();
          _showSuccessDialog(
            'Ad-Free Access Granted!',
            'You now have 2 hours of ad-free experience.',
          );
        }
      } else {
        if (mounted) {
          _showErrorDialog(
            'Ad Not Available',
            'The ad could not be shown. Please try again later.',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorDialog('Error', 'Failed to load ad. Please try again later.');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _retryLoadingAds() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final adMobService = AdMobService();
      await adMobService.loadRewardedAd();

      // Wait a bit for the ad to load
      await Future.delayed(const Duration(seconds: 3));

      if (mounted) {
        setState(() {
          // This will trigger a rebuild and update the UI
        });

        if (adMobService.isRewardedAdLoaded) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Ads loaded successfully! You can now watch ads.'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Still loading ads. Please wait a moment and try again.',
              ),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load ads. Please check your connection.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showSuccessDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.surfaceColor,
        title: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 24),
            const SizedBox(width: 8),
            Text(title, style: TextStyle(color: AppTheme.textPrimary)),
          ],
        ),
        content: Text(message, style: TextStyle(color: AppTheme.textSecondary)),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Great!',
              style: TextStyle(color: AppTheme.primaryBlue),
            ),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.surfaceColor,
        title: Row(
          children: [
            Icon(Icons.error, color: Colors.red, size: 24),
            const SizedBox(width: 8),
            Text(title, style: TextStyle(color: AppTheme.textPrimary)),
          ],
        ),
        content: Text(message, style: TextStyle(color: AppTheme.textSecondary)),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('OK', style: TextStyle(color: AppTheme.primaryBlue)),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: AppTheme.surfaceColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: Row(
        children: [
          Icon(
            widget.isPremiumContent ? Icons.diamond : Icons.block_flipped,
            color: AppTheme.primaryAccent,
            size: 24,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              widget.title,
              style: TextStyle(
                color: AppTheme.textPrimary,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),

      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            widget.description,
            style: TextStyle(color: AppTheme.textSecondary, fontSize: 15),
          ),
          const SizedBox(height: 20),
          if (_premiumStatus != null) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.primaryBlue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.primaryBlue.withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: AppTheme.primaryBlue,
                        size: 18,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Current Status',
                        style: TextStyle(
                          color: AppTheme.textPrimary,
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  if (_premiumStatus!['hasPremiumAccess']) ...[
                    Text(
                      '✨ Premium Access: ${_premiumStatus!['remainingPremiumHours']}h remaining',
                      style: TextStyle(color: Colors.green, fontSize: 13),
                    ),
                  ],
                  if (_premiumStatus!['hasAdFreeAccess']) ...[
                    Text(
                      '🚫 Ad-Free: ${_premiumStatus!['remainingAdFreeMinutes']}m remaining',
                      style: TextStyle(color: Colors.blue, fontSize: 13),
                    ),
                  ],
                  Text(
                    'Ads watched today: ${_premiumStatus!['rewardedAdsWatchedToday']}/${_premiumStatus!['maxRewardedAdsPerDay']}',
                    style: TextStyle(
                      color: AppTheme.textSecondary,
                      fontSize: 13,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
          ],

          // Action Buttons
          if (_premiumStatus != null &&
              _premiumStatus!['canWatchRewardedAd']) ...[
            if (widget.isPremiumContent) ...[
              SizedBox(
                height: 50,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _watchAdForPremium,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryAccent,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                  child: _isLoading
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text('Loading...', style: TextStyle(fontSize: 16)),
                          ],
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.play_arrow, size: 20),
                            const SizedBox(width: 8),
                            Text(
                              'Watch Ad for Premium',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                ),
              ),
              const SizedBox(height: 12),
            ],

            // Retry button for loading ads
            if (!AdMobService().isRewardedAdLoaded) ...[
              SizedBox(
                height: 40,
                child: OutlinedButton.icon(
                  onPressed: _isLoading ? null : _retryLoadingAds,
                  icon: Icon(Icons.refresh, size: 18),
                  label: Text('Retry Loading Ads'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppTheme.primaryBlue,
                    side: BorderSide(color: AppTheme.primaryBlue),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 12),
            ],
            SizedBox(
              height: 50,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _watchAdForAdFree,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryBlue,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: _isLoading
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text('Loading...', style: TextStyle(fontSize: 16)),
                        ],
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.block, size: 20),
                          const SizedBox(width: 8),
                          Text(
                            'Watch Ad for Ad-Free',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
              ),
            ),
          ] else ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.orange, size: 20),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Daily ad limit reached (${_premiumStatus!['rewardedAdsWatchedToday']}/${_premiumStatus!['maxRewardedAdsPerDay']})',
                      style: TextStyle(
                        color: Colors.orange,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],

          const SizedBox(height: 16),

          // Cancel Button
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
            child: Text(
              'Cancel',
              style: TextStyle(
                color: AppTheme.textSecondary,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
