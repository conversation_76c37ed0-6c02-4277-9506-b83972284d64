import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import '../services/admob_service.dart';
import '../services/premium_access_service.dart';
import '../services/admob_config_service.dart';

class BannerAdWidget extends StatefulWidget {
  const BannerAdWidget({super.key});

  @override
  State<BannerAdWidget> createState() => _BannerAdWidgetState();
}

class _BannerAdWidgetState extends State<BannerAdWidget> {
  final AdMobService _adMobService = AdMobService();

  @override
  Widget build(BuildContext context) {
    if (!_adMobService.isBannerAdLoaded || _adMobService.bannerAd == null) {
      return const SizedBox.shrink();
    }

    return Container(
      alignment: Alignment.center,
      width: _adMobService.bannerAd!.size.width.toDouble(),
      height: _adMobService.bannerAd!.size.height.toDouble(),
      child: AdWidget(ad: _adMobService.bannerAd!),
    );
  }
}

class SmartBannerAdWidget extends StatefulWidget {
  final EdgeInsets? margin;
  final Color? backgroundColor;

  const SmartBannerAdWidget({super.key, this.margin, this.backgroundColor});

  @override
  State<SmartBannerAdWidget> createState() => _SmartBannerAdWidgetState();
}

class _SmartBannerAdWidgetState extends State<SmartBannerAdWidget> {
  final AdMobService _adMobService = AdMobService();
  bool _hasAdFreeAccess = false;
  bool _adsEnabled = true;

  @override
  void initState() {
    super.initState();
    _checkAdStatus();
  }

  Future<void> _checkAdStatus() async {
    final hasAccess = await PremiumAccessService.hasAdFreeAccess();
    final adsEnabled = await AdMobConfigService.areAdsEnabled();
    if (mounted) {
      setState(() {
        _hasAdFreeAccess = hasAccess;
        _adsEnabled = adsEnabled;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    print(
      '🎯 Banner Ad Debug: adsEnabled=$_adsEnabled, hasAdFreeAccess=$_hasAdFreeAccess',
    );
    print(
      '🎯 Banner Ad Debug: isBannerAdLoaded=${_adMobService.isBannerAdLoaded}, bannerAd=${_adMobService.bannerAd != null}',
    );

    // Don't show banner ads if ads are disabled or user has ad-free access
    if (!_adsEnabled || _hasAdFreeAccess) {
      print('🎯 Banner Ad Hidden: ads disabled or user has ad-free access');
      return const SizedBox.shrink();
    }

    if (!_adMobService.isBannerAdLoaded || _adMobService.bannerAd == null) {
      print('🎯 Banner Ad Hidden: ad not loaded or null');
      return const SizedBox.shrink();
    }

    print('🎯 Banner Ad Showing!');

    return Container(
      margin: widget.margin ?? const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? Colors.grey[100],
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4.0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8.0),
        child: SizedBox(
          width: _adMobService.bannerAd!.size.width.toDouble(),
          height: _adMobService.bannerAd!.size.height.toDouble(),
          child: AdWidget(ad: _adMobService.bannerAd!),
        ),
      ),
    );
  }
}
