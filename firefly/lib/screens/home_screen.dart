import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/rendering.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../providers/wallpaper_provider.dart';
import '../providers/auth_provider.dart';
import '../models/wallpaper_model.dart';
import '../models/category_model.dart';
import '../utils/app_theme.dart';
import '../utils/url_utils.dart';
import '../widgets/app_drawer.dart';
import '../services/notification_service.dart';
import '../services/analytics_service.dart';
import '../services/rating_prompt_service.dart';
import '../widgets/banner_ad_widget.dart';

import 'wallpaper_preview_screen.dart';
import 'admin_panel_screen.dart';
import 'profile_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {
  final ScrollController _scrollController = ScrollController();
  bool _isScrolling = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadWallpapers();
      _setStatusBarStyle();
      _checkWallpaperSetStatus(); // Check if app was restarted due to wallpaper setting
      // Set context for notification service
      NotificationService.setContext(context);
      // Track screen view (works even without auth)
      try {
        AnalyticsService.trackScreenView(screenName: 'home_screen');
      } catch (e) {
        print('Analytics tracking failed (no auth): $e');
      }
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // App came back to foreground, refresh wallpapers to get latest content
      print('🔄 App resumed, refreshing wallpapers...');
      _loadWallpapers();
    }
  }

  void _onScroll() {
    if (_scrollController.position.userScrollDirection ==
            ScrollDirection.forward ||
        _scrollController.position.userScrollDirection ==
            ScrollDirection.reverse) {
      if (!_isScrolling) {
        setState(() {
          _isScrolling = true;
        });
        _hideStatusBar();

        // Show status bar again after scroll stops
        Future.delayed(const Duration(milliseconds: 1500), () {
          if (mounted) {
            setState(() {
              _isScrolling = false;
            });
            _showStatusBar();
          }
        });
      }
    }
  }

  void _setStatusBarStyle() {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.dark,
      ),
    );
  }

  void _hideStatusBar() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
  }

  void _showStatusBar() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    _setStatusBarStyle();
  }

  Future<void> _loadWallpapers() async {
    final wallpaperProvider = Provider.of<WallpaperProvider>(
      context,
      listen: false,
    );
    await wallpaperProvider.loadWallpapers();
  }

  /// Check if app was restarted due to wallpaper setting and show feedback
  Future<void> _checkWallpaperSetStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final wallpaperSetSuccess =
          prefs.getBool('wallpaper_set_success') ?? false;
      final lastSetWallpaperId = prefs.getString('last_set_wallpaper_id') ?? '';

      if (wallpaperSetSuccess && lastSetWallpaperId.isNotEmpty) {
        // Clear the success flag
        await prefs.setBool('wallpaper_set_success', false);
        await prefs.remove('last_set_wallpaper_id');

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                '🎉 Wallpaper set successfully!',
                style: TextStyle(color: Colors.white),
              ),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      }
    } catch (e) {
      print('Error checking wallpaper set status: $e');
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      drawer: const AppDrawer(),
      body: RefreshIndicator(
        onRefresh: _loadWallpapers,
        color: AppTheme.primaryBlue,
        backgroundColor: AppTheme.surfaceColor,
        child: CustomScrollView(
          slivers: [
            SliverAppBar(
              expandedHeight: 120,
              floating: false,
              pinned: true,
              backgroundColor: AppTheme.surfaceColor,
              elevation: 0,
              surfaceTintColor: Colors.transparent,
              flexibleSpace: FlexibleSpaceBar(
                background: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [AppTheme.surfaceColor, AppTheme.backgroundColor],
                    ),
                  ),
                ),
                title: Text(
                  'FireFly',
                  style: TextStyle(
                    color: AppTheme.textPrimary,
                    fontSize: 28,
                    fontWeight: FontWeight.w300,
                    letterSpacing: 2.0,
                  ),
                ),
                centerTitle: true,
              ),
              leading: Builder(
                builder: (context) => IconButton(
                  icon: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryBlue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppTheme.borderColor,
                        width: 0.5,
                      ),
                    ),
                    child: Icon(
                      Icons.menu,
                      color: AppTheme.textPrimary,
                      size: 20,
                    ),
                  ),
                  onPressed: () => Scaffold.of(context).openDrawer(),
                ),
              ),
              actions: [
                Padding(
                  padding: const EdgeInsets.only(right: 16),
                  child: IconButton(
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryBlue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: AppTheme.borderColor,
                          width: 0.5,
                        ),
                      ),
                      child: Icon(
                        Icons.person,
                        color: AppTheme.textPrimary,
                        size: 20,
                      ),
                    ),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const ProfileScreen(),
                        ),
                      );
                    },
                  ),
                ),
                Consumer<AuthProvider>(
                  builder: (context, authProvider, child) {
                    if (authProvider.isAdmin) {
                      return Padding(
                        padding: const EdgeInsets.only(right: 16),
                        child: IconButton(
                          icon: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: AppTheme.primaryAccent.withValues(
                                alpha: 0.1,
                              ),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: AppTheme.primaryAccent.withValues(
                                  alpha: 0.3,
                                ),
                                width: 0.5,
                              ),
                            ),
                            child: Icon(
                              Icons.admin_panel_settings,
                              color: AppTheme.primaryAccent,
                              size: 20,
                            ),
                          ),
                          onPressed: () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (_) => const AdminPanelScreen(),
                              ),
                            );
                          },
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ],
            ),
            SliverToBoxAdapter(
              child: Container(
                margin: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 16,
                ),
                child: Consumer<WallpaperProvider>(
                  builder: (context, wallpaperProvider, child) {
                    // Refresh categories if empty (only once)
                    if (wallpaperProvider.categories.isEmpty &&
                        !wallpaperProvider.isLoading) {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        wallpaperProvider.loadWallpapers();
                      });
                    }

                    return Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.surfaceColor,
                        borderRadius: BorderRadius.circular(25),
                        border: Border.all(
                          color: AppTheme.borderColor,
                          width: 1,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: AppTheme.textSecondary.withValues(
                              alpha: 0.1,
                            ),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: wallpaperProvider.selectedCategory,
                          isExpanded: true,
                          icon: Icon(
                            Icons.keyboard_arrow_down,
                            color: AppTheme.textSecondary,
                          ),
                          dropdownColor: AppTheme.surfaceColor,
                          style: TextStyle(
                            color: AppTheme.textPrimary,
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                          ),
                          items: wallpaperProvider.categories
                              .map(
                                (category) => DropdownMenuItem(
                                  value: category.name == 'All'
                                      ? null
                                      : category.name,
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.category_outlined,
                                        color: AppTheme.textSecondary,
                                        size: 18,
                                      ),
                                      const SizedBox(width: 12),
                                      Text(
                                        category.name,
                                        style: TextStyle(
                                          color: AppTheme.textPrimary,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              )
                              .toList(),
                          onChanged: (value) {
                            wallpaperProvider.setSelectedCategory(value);
                          },
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),

            // Banner Ad after category selection
            const SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                child: SmartBannerAdWidget(),
              ),
            ),
            Consumer<WallpaperProvider>(
              builder: (context, wallpaperProvider, child) {
                if (wallpaperProvider.isLoading &&
                    wallpaperProvider.wallpapers.isEmpty) {
                  return SliverToBoxAdapter(
                    child: SizedBox(
                      height: 200,
                      child: Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppTheme.primaryBlue,
                          ),
                          strokeWidth: 2,
                        ),
                      ),
                    ),
                  );
                }

                if (wallpaperProvider.errorMessage != null) {
                  return SliverToBoxAdapter(
                    child: _buildErrorWidget(wallpaperProvider.errorMessage!),
                  );
                }

                if (wallpaperProvider.wallpapers.isEmpty) {
                  return SliverToBoxAdapter(child: _buildEmptyWidget());
                }

                return SliverPadding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  sliver: SliverGrid(
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 20,
                          mainAxisSpacing: 20,
                          childAspectRatio: 9 / 16,
                        ),
                    delegate: SliverChildBuilderDelegate((context, index) {
                      // Show wallpaper directly without ad logic for now
                      if (index < wallpaperProvider.wallpapers.length) {
                        final wallpaper = wallpaperProvider.wallpapers[index];
                        return _buildWallpaperCard(wallpaper);
                      }

                      // Return empty container if no more wallpapers
                      return const SizedBox.shrink();
                    }, childCount: wallpaperProvider.wallpapers.length),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingGrid() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 9 / 16, // Instagram Reels aspect ratio
        ),
        itemCount: 10,
        itemBuilder: (context, index) {
          return Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.white.withValues(alpha: 0.4),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildErrorWidget(String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'Oops! Something went wrong',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadWallpapers,
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return Consumer<WallpaperProvider>(
      builder: (context, wallpaperProvider, child) {
        return Center(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.image_not_supported_outlined,
                  size: 64,
                  color: AppTheme.textSecondary,
                ),
                const SizedBox(height: 16),
                Text(
                  'No wallpapers found',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Text(
                  wallpaperProvider.selectedCategory == null
                      ? 'Check back later for new wallpapers'
                      : 'No wallpapers in "${wallpaperProvider.selectedCategory}" category',
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Text(
                  'Debug Info:',
                  style: Theme.of(
                    context,
                  ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Text(
                  'Selected category: ${wallpaperProvider.selectedCategory ?? "All"}',
                  style: Theme.of(context).textTheme.bodySmall,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: _loadWallpapers,
                  child: const Text('Refresh'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildWallpaperGrid(List wallpapers) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        controller: _scrollController,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 9 / 16, // Instagram Reels aspect ratio
        ),
        itemCount: wallpapers.length,
        itemBuilder: (context, index) {
          final wallpaper = wallpapers[index];
          return _buildWallpaperCard(wallpaper);
        },
      ),
    );
  }

  Widget _buildWallpaperCard(dynamic wallpaper) {
    // Fix duplicate folder path in URL automatically for any category
    String imageUrl = UrlUtils.fixDuplicateFolderPaths(wallpaper.imageUrl);

    return GestureDetector(
      onTap: () {
        // Track wallpaper view for rating prompt
        RatingPromptService.trackWallpaperView();

        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (_) => WallpaperPreviewScreen(wallpaper: wallpaper),
          ),
        );
      },
      child: Hero(
        tag: 'wallpaper_${wallpaper.id}',
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: AppTheme.textSecondary.withValues(alpha: 0.15),
                blurRadius: 12,
                offset: const Offset(0, 4),
                spreadRadius: 0,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: Stack(
              fit: StackFit.expand,
              children: [
                CachedNetworkImage(
                  imageUrl: imageUrl,
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: double.infinity,
                  memCacheWidth: 300, // Further reduce memory cache size
                  memCacheHeight: 450, // Further reduce memory cache size
                  maxWidthDiskCache: 300, // Further reduce disk cache size
                  maxHeightDiskCache: 450, // Further reduce disk cache size
                  filterQuality: FilterQuality
                      .medium, // Reduce image quality for thumbnails
                  placeholder: (context, url) => Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          AppTheme.backgroundColor,
                          AppTheme.surfaceColor,
                        ],
                      ),
                    ),
                    child: Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppTheme.primaryBlue,
                        ),
                        strokeWidth: 2,
                      ),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          AppTheme.backgroundColor,
                          AppTheme.surfaceColor,
                        ],
                      ),
                    ),
                    child: Icon(
                      Icons.broken_image_outlined,
                      color: AppTheme.textTertiary,
                      size: 48,
                    ),
                  ),
                ),

                // Gradient overlay
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    height: 100,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          AppTheme.textPrimary.withValues(alpha: 0.8),
                        ],
                      ),
                    ),
                  ),
                ),

                // Clean wallpaper view without category labels

                // Category label
                Positioned(
                  bottom: 12,
                  left: 12,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.surfaceColor.withValues(alpha: 0.9),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppTheme.borderColor, width: 1),
                      boxShadow: [
                        BoxShadow(
                          color: AppTheme.textSecondary.withValues(alpha: 0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Text(
                      wallpaper.category,
                      style: TextStyle(
                        color: AppTheme.textPrimary,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),

                // Favorite button
                Positioned(
                  top: 12,
                  right: 12,
                  child: Consumer<WallpaperProvider>(
                    builder: (context, provider, child) {
                      final isFavorite = provider.isFavorite(wallpaper.id);
                      return GestureDetector(
                        onTap: () => provider.toggleFavorite(wallpaper.id),
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: AppTheme.surfaceColor.withValues(alpha: 0.9),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: AppTheme.borderColor,
                              width: 1,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: AppTheme.textSecondary.withValues(
                                  alpha: 0.1,
                                ),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Icon(
                            isFavorite ? Icons.favorite : Icons.favorite_border,
                            color: isFavorite
                                ? AppTheme.errorColor
                                : AppTheme.textSecondary,
                            size: 18,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
