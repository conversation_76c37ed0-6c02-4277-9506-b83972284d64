import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/app_theme.dart';
import '../services/admob_config_service.dart';
import '../services/admob_service.dart';

class AdMobManagementScreen extends StatefulWidget {
  const AdMobManagementScreen({super.key});

  @override
  State<AdMobManagementScreen> createState() => _AdMobManagementScreenState();
}

class _AdMobManagementScreenState extends State<AdMobManagementScreen> {
  final _formKey = GlobalKey<FormState>();

  // Controllers for form fields
  final _androidAppIdController = TextEditingController();
  final _iosAppIdController = TextEditingController();
  final _androidBannerController = TextEditingController();
  final _iosBannerController = TextEditingController();
  final _androidInterstitialController = TextEditingController();
  final _iosInterstitialController = TextEditingController();
  final _androidRewardedController = TextEditingController();
  final _iosRewardedController = TextEditingController();

  bool _adsEnabled = true;
  bool _isLoading = false;
  bool _isSaving = false;
  String _configStatus = 'Loading...';

  @override
  void initState() {
    super.initState();
    _loadCurrentConfig();
  }

  @override
  void dispose() {
    _androidAppIdController.dispose();
    _iosAppIdController.dispose();
    _androidBannerController.dispose();
    _iosBannerController.dispose();
    _androidInterstitialController.dispose();
    _iosInterstitialController.dispose();
    _androidRewardedController.dispose();
    _iosRewardedController.dispose();
    super.dispose();
  }

  Future<void> _loadCurrentConfig() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final config = await AdMobConfigService.getAllConfig();
      final status = await AdMobConfigService.getConfigStatus();

      setState(() {
        _adsEnabled = config['adsEnabled'];
        _androidAppIdController.text = config['androidAppId'];
        _iosAppIdController.text = config['iosAppId'];
        _androidBannerController.text = config['androidBannerId'];
        _iosBannerController.text = config['iosBannerId'];
        _androidInterstitialController.text = config['androidInterstitialId'];
        _iosInterstitialController.text = config['iosInterstitialId'];
        _androidRewardedController.text = config['androidRewardedId'];
        _iosRewardedController.text = config['iosRewardedId'];
        _configStatus = status;
      });
    } catch (e) {
      _showSnackBar('Failed to load configuration: $e', isError: true);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveConfig() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final config = {
        'adsEnabled': _adsEnabled,
        'androidAppId': _androidAppIdController.text.trim(),
        'iosAppId': _iosAppIdController.text.trim(),
        'androidBannerId': _androidBannerController.text.trim(),
        'iosBannerId': _iosBannerController.text.trim(),
        'androidInterstitialId': _androidInterstitialController.text.trim(),
        'iosInterstitialId': _iosInterstitialController.text.trim(),
        'androidRewardedId': _androidRewardedController.text.trim(),
        'iosRewardedId': _iosRewardedController.text.trim(),
      };

      await AdMobConfigService.saveAllConfig(config);

      // Reload ads with new configuration
      final adMobService = AdMobService();
      await adMobService.loadBannerAd();
      await adMobService.loadInterstitialAd();
      await adMobService.loadRewardedAd();

      final status = await AdMobConfigService.getConfigStatus();
      setState(() {
        _configStatus = status;
      });

      _showSnackBar('AdMob configuration saved successfully!');
    } catch (e) {
      _showSnackBar('Failed to save configuration: $e', isError: true);
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  Future<void> _resetToDefaults() async {
    final confirmed = await _showConfirmDialog(
      'Reset to Defaults',
      'This will reset all AdMob settings to default test values. Are you sure?',
    );

    if (!confirmed) return;

    setState(() {
      _isSaving = true;
    });

    try {
      await AdMobConfigService.resetToDefaults();
      await _loadCurrentConfig();
      _showSnackBar('Configuration reset to defaults');
    } catch (e) {
      _showSnackBar('Failed to reset configuration: $e', isError: true);
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  Future<void> _toggleAds() async {
    final newState = !_adsEnabled;
    final action = newState ? 'enable' : 'disable';

    final confirmed = await _showConfirmDialog(
      '${action.toUpperCase()} Ads',
      'Are you sure you want to $action ads in the app?',
    );

    if (!confirmed) return;

    setState(() {
      _isSaving = true;
    });

    try {
      await AdMobConfigService.setAdsEnabled(newState);
      setState(() {
        _adsEnabled = newState;
      });

      final status = await AdMobConfigService.getConfigStatus();
      setState(() {
        _configStatus = status;
      });

      _showSnackBar('Ads ${newState ? 'enabled' : 'disabled'} successfully!');
    } catch (e) {
      _showSnackBar('Failed to $action ads: $e', isError: true);
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  Future<bool> _showConfirmDialog(String title, String message) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            backgroundColor: AppTheme.surfaceColor,
            title: Text(title, style: TextStyle(color: AppTheme.textPrimary)),
            content: Text(
              message,
              style: TextStyle(color: AppTheme.textSecondary),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text(
                  'Cancel',
                  style: TextStyle(color: AppTheme.textSecondary),
                ),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryBlue,
                ),
                child: const Text(
                  'Confirm',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        ) ??
        false;
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : AppTheme.primaryGreen,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  String? _validateAdUnitId(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Ad Unit ID is required';
    }

    if (!AdMobConfigService.isValidAdUnitId(value.trim())) {
      return 'Invalid Ad Unit ID format';
    }

    return null;
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppTheme.borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppTheme.primaryBlue),
        ),
      ),
      validator: validator,
      style: TextStyle(color: AppTheme.textPrimary),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AdMob Management'),
        backgroundColor: AppTheme.primaryBlue,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _isLoading || _isSaving ? null : _loadCurrentConfig,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Status Card
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  _adsEnabled ? Icons.ads_click : Icons.block,
                                  color: _adsEnabled
                                      ? AppTheme.primaryGreen
                                      : Colors.red,
                                  size: 24,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Ad Status: $_configStatus',
                                        style: Theme.of(
                                          context,
                                        ).textTheme.titleMedium,
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        _adsEnabled
                                            ? 'Ads are currently enabled'
                                            : 'Ads are currently disabled',
                                        style: TextStyle(
                                          color: _adsEnabled
                                              ? AppTheme.primaryGreen
                                              : Colors.red,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                ElevatedButton.icon(
                                  onPressed: _isSaving ? null : _toggleAds,
                                  icon: Icon(
                                    _adsEnabled
                                        ? Icons.block
                                        : Icons.play_arrow,
                                  ),
                                  label: Text(
                                    _adsEnabled ? 'Disable Ads' : 'Enable Ads',
                                  ),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: _adsEnabled
                                        ? Colors.red
                                        : AppTheme.primaryGreen,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // App IDs Section
                    Text(
                      'App IDs',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 12),

                    _buildTextField(
                      controller: _androidAppIdController,
                      label: 'Android App ID',
                      hint: 'ca-app-pub-XXXXXXXXXXXXXXXX~XXXXXXXXXX',
                      validator: _validateAdUnitId,
                    ),
                    const SizedBox(height: 16),

                    _buildTextField(
                      controller: _iosAppIdController,
                      label: 'iOS App ID',
                      hint: 'ca-app-pub-XXXXXXXXXXXXXXXX~XXXXXXXXXX',
                      validator: _validateAdUnitId,
                    ),

                    const SizedBox(height: 24),

                    // Banner Ad IDs Section
                    Text(
                      'Banner Ad Unit IDs',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 12),

                    _buildTextField(
                      controller: _androidBannerController,
                      label: 'Android Banner Ad ID',
                      hint: 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX',
                      validator: _validateAdUnitId,
                    ),
                    const SizedBox(height: 16),

                    _buildTextField(
                      controller: _iosBannerController,
                      label: 'iOS Banner Ad ID',
                      hint: 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX',
                      validator: _validateAdUnitId,
                    ),

                    const SizedBox(height: 24),

                    // Interstitial Ad IDs Section
                    Text(
                      'Interstitial Ad Unit IDs',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 12),

                    _buildTextField(
                      controller: _androidInterstitialController,
                      label: 'Android Interstitial Ad ID',
                      hint: 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX',
                      validator: _validateAdUnitId,
                    ),
                    const SizedBox(height: 16),

                    _buildTextField(
                      controller: _iosInterstitialController,
                      label: 'iOS Interstitial Ad ID',
                      hint: 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX',
                      validator: _validateAdUnitId,
                    ),

                    const SizedBox(height: 24),

                    // Rewarded Ad IDs Section
                    Text(
                      'Rewarded Ad Unit IDs',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 12),

                    _buildTextField(
                      controller: _androidRewardedController,
                      label: 'Android Rewarded Ad ID',
                      hint: 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX',
                      validator: _validateAdUnitId,
                    ),
                    const SizedBox(height: 16),

                    _buildTextField(
                      controller: _iosRewardedController,
                      label: 'iOS Rewarded Ad ID',
                      hint: 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX',
                      validator: _validateAdUnitId,
                    ),

                    const SizedBox(height: 32),

                    // Action Buttons
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: _isSaving ? null : _resetToDefaults,
                            icon: const Icon(Icons.restore),
                            label: const Text('Reset to Defaults'),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: Colors.orange,
                              side: const BorderSide(color: Colors.orange),
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isSaving ? null : _saveConfig,
                            icon: _isSaving
                                ? const SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white,
                                      ),
                                    ),
                                  )
                                : const Icon(Icons.save),
                            label: Text(
                              _isSaving ? 'Saving...' : 'Save Configuration',
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme.primaryBlue,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Instructions Card
                    Card(
                      color: AppTheme.primaryBlue.withValues(alpha: 0.1),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  color: AppTheme.primaryBlue,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Instructions',
                                  style: Theme.of(context).textTheme.titleMedium
                                      ?.copyWith(color: AppTheme.primaryBlue),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            const Text(
                              '• Get your AdMob App IDs and Ad Unit IDs from Google AdMob console\n'
                              '• Use test IDs during development and switch to production IDs for release\n'
                              '• You can enable/disable ads instantly with the toggle button\n'
                              '• Changes take effect immediately after saving\n'
                              '• Reset to defaults will restore test ad unit IDs\n'
                              '• Make sure to test ads before publishing to production',
                              style: TextStyle(height: 1.5),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
