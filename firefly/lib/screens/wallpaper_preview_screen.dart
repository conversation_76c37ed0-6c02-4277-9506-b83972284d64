import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:photo_view/photo_view.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:gal/gal.dart';
import 'dart:io';
import 'dart:ui' as ui;
import 'dart:typed_data';
import '../utils/app_theme.dart';
import '../utils/url_utils.dart';
import '../providers/auth_provider.dart';
import '../providers/wallpaper_provider.dart';
import '../services/analytics_service.dart';
import '../services/user_management_service.dart';
import '../services/rating_prompt_service.dart';
import '../services/admob_service.dart';
import '../services/premium_access_service.dart';
import '../widgets/app_sharing_widget.dart';
import '../widgets/premium_access_dialog.dart';

class WallpaperPreviewScreen extends StatefulWidget {
  final dynamic wallpaper;

  const WallpaperPreviewScreen({super.key, required this.wallpaper});

  @override
  State<WallpaperPreviewScreen> createState() => _WallpaperPreviewScreenState();
}

class _WallpaperPreviewScreenState extends State<WallpaperPreviewScreen>
    with TickerProviderStateMixin {
  late AnimationController _buttonController;
  late Animation<double> _buttonAnimation;
  bool _isLoading = false;
  bool _showButtons = true;
  int _viewCount = 0;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _incrementViewCount();
    _loadViewCount();
    _trackWallpaperView();
  }

  // Track wallpaper view for analytics
  void _trackWallpaperView() {
    try {
      AnalyticsService.trackWallpaperView(
        wallpaperId: widget.wallpaper.id,
        category: widget.wallpaper.category,
        source: 'preview_screen',
      );

      // Update user stats only if user is authenticated
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      if (authProvider.user != null) {
        UserManagementService.updateUserStats(
          userId: authProvider.user!.uid,
          incrementField: 'wallpapersViewed',
        );
      } else {
        print('📊 Wallpaper view tracked without user stats (no auth)');
      }
    } catch (e) {
      print('Error tracking wallpaper view: $e');
    }
  }

  void _initializeAnimations() {
    _buttonController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _buttonAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _buttonController, curve: Curves.easeInOut),
    );

    _buttonController.forward();
  }

  @override
  void dispose() {
    _buttonController.dispose();
    super.dispose();
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.grey[900],
          title: const Text(
            'Delete Wallpaper',
            style: TextStyle(color: Colors.white),
          ),
          content: const Text(
            'Are you sure you want to permanently delete this wallpaper? This action cannot be undone.',
            style: TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel', style: TextStyle(color: Colors.grey)),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteWallpaper();
              },
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  Future<void> _deleteWallpaper() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final wallpaperProvider = Provider.of<WallpaperProvider>(
        context,
        listen: false,
      );

      if (authProvider.user?.email == null) {
        _showSnackBar('Error: User not authenticated', isError: true);
        return;
      }

      // Extract public_id from the wallpaper URL
      final publicId = wallpaperProvider.extractPublicIdFromUrl(
        widget.wallpaper.imageUrl,
      );

      final success = await wallpaperProvider.deleteWallpaper(
        wallpaperId: widget.wallpaper.id,
        publicId: publicId,
        userEmail: authProvider.user!.email!,
      );

      if (success) {
        _showSnackBar('Wallpaper deleted successfully');
        // Navigate back to home screen
        if (mounted) {
          Navigator.of(context).pop();
        }
      } else {
        _showSnackBar(
          wallpaperProvider.errorMessage ?? 'Failed to delete wallpaper',
          isError: true,
        );
      }
    } catch (e) {
      _showSnackBar('Error: ${e.toString()}', isError: true);
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _toggleButtons() {
    setState(() {
      _showButtons = !_showButtons;
      if (_showButtons) {
        _buttonController.forward();
      } else {
        _buttonController.reverse();
      }
    });
  }

  Future<void> _downloadWallpaper() async {
    // Check if user has ad-free access
    final hasAdFreeAccess = await PremiumAccessService.hasAdFreeAccess();

    if (!hasAdFreeAccess) {
      // Show interstitial ad before download
      final adShown = await AdMobService().showInterstitialAd();
      if (adShown) {
        // Small delay to let ad complete
        await Future.delayed(const Duration(milliseconds: 500));
      }
    }

    await _performDownload();
  }

  Future<void> _performDownload() async {
    try {
      setState(() => _isLoading = true);

      // Request storage permission based on Android version
      bool hasPermission = false;

      if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;

        if (androidInfo.version.sdkInt >= 33) {
          // Android 13+ (API 33+) - Request media permissions
          final permission = await Permission.photos.request();
          hasPermission = permission.isGranted;
        } else if (androidInfo.version.sdkInt >= 30) {
          // Android 11+ (API 30+) - Request manage external storage
          final permission = await Permission.manageExternalStorage.request();
          hasPermission = permission.isGranted;
        } else {
          // Android 10 and below - Request storage permission
          final permission = await Permission.storage.request();
          hasPermission = permission.isGranted;
        }
      } else {
        // iOS - Request photos permission
        final permission = await Permission.photos.request();
        hasPermission = permission.isGranted;
      }

      if (!hasPermission) {
        _showSnackBar(
          'Storage permission is required to download wallpapers',
          isError: true,
        );
        return;
      }

      // Get the image URL
      final String downloadUrl = _getFixedImageUrl(
        widget.wallpaper.hdUrl.isNotEmpty
            ? widget.wallpaper.hdUrl
            : widget.wallpaper.imageUrl,
      );

      // Download image to memory first
      final dio = Dio();
      final response = await dio.get(
        downloadUrl,
        options: Options(responseType: ResponseType.bytes),
      );

      // Save image to gallery using Gal
      await Gal.putImageBytes(response.data, album: "FireFly Wallpapers");

      _showSnackBar('Wallpaper saved to gallery successfully!');

      // Track download analytics
      try {
        await AnalyticsService.trackWallpaperDownload(
          wallpaperId: widget.wallpaper.id,
          category: widget.wallpaper.category,
          quality: 'standard',
          source: 'preview_screen',
        );

        // Track download for rating prompt
        await RatingPromptService.trackWallpaperDownload();

        // Update user stats (get auth provider before async operations)
        if (mounted) {
          final authProvider = Provider.of<AuthProvider>(
            context,
            listen: false,
          );
          final user = authProvider.user;
          if (user != null) {
            await UserManagementService.updateUserStats(
              userId: user.uid,
              incrementField: 'wallpapersDownloaded',
            );
          }
        }
      } catch (e) {
        print('Error tracking download: $e');
      }
    } catch (e) {
      _showSnackBar(
        'Failed to download wallpaper: ${e.toString()}',
        isError: true,
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _setWallpaper(int location) async {
    // Show perfect fit tips dialog first
    await _showPerfectFitTips(location);
  }

  Future<void> _showPerfectFitTips(int location) async {
    String locationText = '';
    switch (location) {
      case 1:
        locationText = 'Home Screen';
        break;
      case 2:
        locationText = 'Lock Screen';
        break;
      case 3:
        locationText = 'Both Screens';
        break;
    }

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1E1E1E),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            const Icon(Icons.tips_and_updates, color: Colors.amber, size: 24),
            const SizedBox(width: 8),
            const Text(
              'Perfect Fit Tips',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Setting wallpaper for: $locationText',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              '✨ Smart Wallpaper Setting:',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              '• Wallpaper automatically scales to fit your screen perfectly',
              style: TextStyle(color: Colors.white70, fontSize: 14),
            ),
            const SizedBox(height: 8),
            const Text(
              '• Image will be centered and cropped for best fit',
              style: TextStyle(color: Colors.white70, fontSize: 14),
            ),
            const SizedBox(height: 8),
            const Text(
              '• No black bars or stretching - perfect coverage guaranteed',
              style: TextStyle(color: Colors.white70, fontSize: 14),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
              ),
              child: const Row(
                children: [
                  Icon(Icons.auto_fix_high, color: Colors.green, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Advanced image processing ensures perfect fit and center alignment!',
                      style: TextStyle(color: Colors.green, fontSize: 13),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'Cancel',
              style: TextStyle(color: Colors.white70),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _proceedWithWallpaperSetting(location);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF1976D2),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'Set Wallpaper',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _proceedWithWallpaperSetting(int location) async {
    // Direct wallpaper setting without ads
    await _performSetWallpaper(location);
  }

  /// Process image for proper wallpaper fitting and centering
  Future<Uint8List> _processImageForWallpaper(Uint8List imageBytes) async {
    try {
      // Get device screen dimensions
      final mediaQuery = MediaQuery.of(context);
      final screenWidth = mediaQuery.size.width * mediaQuery.devicePixelRatio;
      final screenHeight = mediaQuery.size.height * mediaQuery.devicePixelRatio;

      // Decode the original image
      final ui.Codec codec = await ui.instantiateImageCodec(imageBytes);
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      final ui.Image originalImage = frameInfo.image;

      // Calculate scaling to cover screen while maintaining aspect ratio (no black bars)
      final double imageAspectRatio =
          originalImage.width / originalImage.height;
      final double screenAspectRatio = screenWidth / screenHeight;

      double scaledWidth, scaledHeight;
      double offsetX = 0, offsetY = 0;

      if (imageAspectRatio > screenAspectRatio) {
        // Image is wider than screen ratio - scale to fit height, crop sides
        scaledHeight = screenHeight;
        scaledWidth = scaledHeight * imageAspectRatio;
        offsetX = (screenWidth - scaledWidth) / 2;
      } else {
        // Image is taller than screen ratio - scale to fit width, crop top/bottom
        scaledWidth = screenWidth;
        scaledHeight = scaledWidth / imageAspectRatio;
        offsetY = (screenHeight - scaledHeight) / 2;
      }

      // Create a new canvas with screen dimensions
      final ui.PictureRecorder recorder = ui.PictureRecorder();
      final Canvas canvas = Canvas(recorder);

      // Fill background with black (in case image doesn't cover full screen)
      final Paint backgroundPaint = Paint()..color = Colors.black;
      canvas.drawRect(
        Rect.fromLTWH(0, 0, screenWidth, screenHeight),
        backgroundPaint,
      );

      // Draw the scaled and centered image
      final Rect srcRect = Rect.fromLTWH(
        0,
        0,
        originalImage.width.toDouble(),
        originalImage.height.toDouble(),
      );
      final Rect dstRect = Rect.fromLTWH(
        offsetX,
        offsetY,
        scaledWidth,
        scaledHeight,
      );

      canvas.drawImageRect(originalImage, srcRect, dstRect, Paint());

      // Convert canvas to image
      final ui.Picture picture = recorder.endRecording();
      final ui.Image processedImage = await picture.toImage(
        screenWidth.round(),
        screenHeight.round(),
      );

      // Convert to bytes
      final ByteData? byteData = await processedImage.toByteData(
        format: ui.ImageByteFormat.png,
      );

      originalImage.dispose();
      processedImage.dispose();

      return byteData!.buffer.asUint8List();
    } catch (e) {
      // If processing fails, return original image
      print('Error processing image for wallpaper: $e');
      return imageBytes;
    }
  }

  /// Set wallpaper using native platform channel (no app restart)
  Future<bool> _setWallpaperSafely(File tempFile, int wallpaperLocation) async {
    try {
      // Save current app state before wallpaper setting
      await _saveAppState();

      // Add a small delay to ensure UI is stable
      await Future.delayed(const Duration(milliseconds: 100));

      // Use platform channel to set wallpaper without app restart
      const platform = MethodChannel('com.anuved.firefly/wallpaper');

      String location;
      switch (wallpaperLocation) {
        case 1: // Home Screen
          location = 'home';
          break;
        case 2: // Lock Screen
          location = 'lock';
          break;
        case 3: // Both Screens
          location = 'both';
          break;
        default:
          return false;
      }

      // Call native method to set wallpaper with timeout for "both" option
      final bool success;
      if (location == 'both') {
        // Give more time for "both screens" operation
        success = await platform
            .invokeMethod('setWallpaper', {
              'filePath': tempFile.path,
              'location': location,
            })
            .timeout(
              const Duration(seconds: 10),
              onTimeout: () {
                print('⏰ Wallpaper setting timeout for both screens');
                return false;
              },
            );
      } else {
        success = await platform.invokeMethod('setWallpaper', {
          'filePath': tempFile.path,
          'location': location,
        });
      }

      // If successful, mark wallpaper as set in preferences
      if (success) {
        await _markWallpaperSetSuccess();
      }

      return success;
    } catch (e) {
      print('❌ Error in _setWallpaperSafely: $e');
      // Fallback: Show system wallpaper picker instead of crashing
      return await _showSystemWallpaperPicker(tempFile);
    }
  }

  /// Fallback: Show system wallpaper picker if native method fails
  Future<bool> _showSystemWallpaperPicker(File tempFile) async {
    try {
      const platform = MethodChannel('com.anuved.firefly/wallpaper');
      return await platform.invokeMethod('showWallpaperPicker', {
        'filePath': tempFile.path,
      });
    } catch (e) {
      print('❌ Fallback wallpaper picker failed: $e');
      return false;
    }
  }

  /// Save app state before wallpaper setting
  Future<void> _saveAppState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('last_wallpaper_id', widget.wallpaper.id);
      await prefs.setString('last_wallpaper_title', widget.wallpaper.title);
      await prefs.setBool('wallpaper_setting_in_progress', true);
      await prefs.setInt(
        'last_wallpaper_timestamp',
        DateTime.now().millisecondsSinceEpoch,
      );
    } catch (e) {
      print('❌ Error saving app state: $e');
    }
  }

  /// Mark wallpaper setting as successful
  Future<void> _markWallpaperSetSuccess() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('wallpaper_setting_in_progress', false);
      await prefs.setBool('wallpaper_set_success', true);
      await prefs.setString('last_set_wallpaper_id', widget.wallpaper.id);
    } catch (e) {
      print('❌ Error marking wallpaper success: $e');
    }
  }

  /// Prevent app restart after wallpaper setting
  Future<void> _preventAppRestart() async {
    try {
      // Keep the app active for a few seconds to prevent restart
      await Future.delayed(const Duration(seconds: 2));

      // Force a rebuild to ensure UI stays responsive
      if (mounted) {
        setState(() {
          // Trigger rebuild
        });
      }
    } catch (e) {
      print('❌ Error in _preventAppRestart: $e');
    }
  }

  Future<void> _performSetWallpaper(int location) async {
    File? tempFile;
    try {
      if (mounted) setState(() => _isLoading = true);

      final String wallpaperUrl = _getFixedImageUrl(
        widget.wallpaper.hdUrl.isNotEmpty
            ? widget.wallpaper.hdUrl
            : widget.wallpaper.imageUrl,
      );
      String result = 'Failed';

      // Download the image first
      final response = await Dio().get(
        wallpaperUrl,
        options: Options(responseType: ResponseType.bytes),
      );

      // For lock screen and both screens, use simpler processing to avoid crashes
      Uint8List imageBytes;
      if (location == 1) {
        // Home screen - use advanced processing
        imageBytes = await _processImageForWallpaper(response.data);
      } else {
        // Lock screen and both screens - use original image to prevent crashes
        imageBytes = response.data;
      }

      final directory = await getTemporaryDirectory();
      tempFile = File(
        '${directory.path}/wallpaper_${location}_${DateTime.now().millisecondsSinceEpoch}.jpg',
      );
      await tempFile.writeAsBytes(imageBytes);

      // Add a small delay to ensure file is written
      await Future.delayed(const Duration(milliseconds: 100));

      bool success = false;
      try {
        // Use async_wallpaper for stable wallpaper setting without app restart
        switch (location) {
          case 1: // Home Screen
            success = await _setWallpaperSafely(tempFile, 1);
            result = success
                ? 'Home screen wallpaper set successfully!'
                : 'Failed to set home screen wallpaper';
            break;
          case 2: // Lock Screen
            success = await _setWallpaperSafely(tempFile, 2);
            result = success
                ? 'Lock screen wallpaper set successfully!'
                : 'Failed to set lock screen wallpaper';
            break;
          case 3: // Both
            success = await _setWallpaperSafely(tempFile, 3);
            result = success
                ? 'Wallpaper set for both screens successfully!'
                : 'Failed to set wallpaper for both screens';
            break;
          default:
            result = 'Invalid location';
        }
      } catch (e) {
        print('❌ Wallpaper setting error: $e');
        success = false;
        result = 'Failed to set wallpaper: ${e.toString()}';
      }

      // Show immediate feedback
      if (mounted) {
        _showSnackBar(result, isError: !success);
      }

      // For all wallpaper settings, show a dialog and prevent app restart
      if (success && mounted) {
        await Future.delayed(const Duration(milliseconds: 500));
        if (mounted) {
          await _showWallpaperSetDialog(result);
        }
      }

      // Additional measures to prevent app restart
      if (success) {
        // Keep the app in foreground
        await _preventAppRestart();
      }
    } catch (e) {
      if (mounted) {
        _showSnackBar(
          'Failed to set wallpaper: ${e.toString()}',
          isError: true,
        );
      }
    } finally {
      // Clean up temporary file
      if (tempFile != null) {
        try {
          if (await tempFile.exists()) {
            await tempFile.delete();
          }
        } catch (e) {
          // Ignore cleanup errors
        }
      }
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// Show a persistent dialog for wallpaper set confirmation
  Future<void> _showWallpaperSetDialog(String message) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PopScope(
        canPop: false,
        child: AlertDialog(
          backgroundColor: AppTheme.surfaceColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 24),
              const SizedBox(width: 8),
              Text(
                'Success!',
                style: TextStyle(
                  color: AppTheme.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          content: Text(
            '$message\n\nYou can continue browsing wallpapers.',
            style: TextStyle(color: AppTheme.textSecondary),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Continue',
                style: TextStyle(color: AppTheme.primaryBlue),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _downloadHDWallpaper() async {
    // Check if user has premium access
    final hasPremiumAccess = await PremiumAccessService.hasPremiumAccess();

    if (!hasPremiumAccess) {
      // Show premium access dialog
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => PremiumAccessDialog(
            title: 'Premium HD Download',
            description:
                'HD wallpapers require premium access. Watch a short ad to unlock 24 hours of premium access to all HD wallpapers.',
            isPremiumContent: true,
            onPremiumGranted: () {
              // After premium is granted, proceed with download
              _performHDDownload();
            },
          ),
        );
      }
      return;
    }

    // User has premium access, show interstitial ad before HD download
    final hasAdFreeAccess = await PremiumAccessService.hasAdFreeAccess();
    if (!hasAdFreeAccess) {
      final adShown = await AdMobService().showInterstitialAd();
      if (adShown) {
        // Small delay to let ad complete
        await Future.delayed(const Duration(milliseconds: 500));
      }
    }

    await _performHDDownload();
  }

  Future<void> _performHDDownload() async {
    try {
      setState(() => _isLoading = true);

      // Request storage permission
      bool hasPermission = false;

      if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;

        if (androidInfo.version.sdkInt >= 33) {
          final permission = await Permission.photos.request();
          hasPermission = permission.isGranted;
        } else if (androidInfo.version.sdkInt >= 30) {
          final permission = await Permission.manageExternalStorage.request();
          hasPermission = permission.isGranted;
        } else {
          final permission = await Permission.storage.request();
          hasPermission = permission.isGranted;
        }
      } else {
        final permission = await Permission.photos.request();
        hasPermission = permission.isGranted;
      }

      if (!hasPermission) {
        _showSnackBar(
          'Storage permission is required to download wallpapers',
          isError: true,
        );
        return;
      }

      // Use HD URL if available, otherwise use regular URL
      final String downloadUrl = _getFixedImageUrl(
        widget.wallpaper.hdUrl.isNotEmpty
            ? widget.wallpaper.hdUrl
            : widget.wallpaper.imageUrl,
      );

      // Download image to memory first
      final dio = Dio();
      final response = await dio.get(
        downloadUrl,
        options: Options(responseType: ResponseType.bytes),
      );

      // Save to gallery with HD prefix
      final fileName =
          'FireFly_HD_${widget.wallpaper.title.replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}.jpg';

      await Gal.putImageBytes(response.data, name: fileName);

      _showSnackBar('HD Wallpaper downloaded successfully! ✨');

      // Track HD download analytics
      try {
        await AnalyticsService.trackWallpaperDownload(
          wallpaperId: widget.wallpaper.id,
          category: widget.wallpaper.category,
          quality: 'hd',
          source: 'preview_screen',
        );

        // Track download for rating prompt
        await RatingPromptService.trackWallpaperDownload();

        // Update user stats (get auth provider before async operations)
        if (mounted) {
          final authProvider = Provider.of<AuthProvider>(
            context,
            listen: false,
          );
          final user = authProvider.user;
          if (user != null) {
            await UserManagementService.updateUserStats(
              userId: user.uid,
              incrementField: 'wallpapersDownloaded',
            );
          }
        }
      } catch (e) {
        print('Error tracking HD download: $e');
      }
    } catch (e) {
      _showSnackBar(
        'Failed to download HD wallpaper: ${e.toString()}',
        isError: true,
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _shareWallpaper() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => WallpaperSharingWidget(
        wallpaperTitle: widget.wallpaper.title,
        wallpaperUrl: widget.wallpaper.imageUrl,
        onShared: () {
          // Track sharing analytics
          AnalyticsService.trackAppSharing(
            action: 'wallpaper_shared',
            shareMethod: 'native_share',
            source: 'preview_screen',
            wallpaperId: widget.wallpaper.id,
            wallpaperTitle: widget.wallpaper.title,
          );
        },
      ),
    );
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : AppTheme.primaryGreen,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  String _getFixedImageUrl(String url) {
    // Use the utility function to fix duplicate folder paths automatically
    String fixedUrl = UrlUtils.fixDuplicateFolderPaths(url);

    // Additional validation to ensure URL is properly formatted
    if (fixedUrl.isEmpty || !UrlUtils.isValidUrl(fixedUrl)) {
      print('⚠️ Invalid URL detected: $fixedUrl');
      return url; // Return original URL as fallback
    }

    return fixedUrl;
  }

  @override
  Widget build(BuildContext context) {
    final String fixedImageUrl = _getFixedImageUrl(
      widget.wallpaper.hdUrl.isNotEmpty
          ? widget.wallpaper.hdUrl
          : widget.wallpaper.imageUrl,
    );

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Wallpaper Image
          GestureDetector(
            onTap: _toggleButtons,
            child: Hero(
              tag: 'wallpaper_${widget.wallpaper.id}',
              child: PhotoView(
                imageProvider: CachedNetworkImageProvider(fixedImageUrl),
                minScale: PhotoViewComputedScale.contained,
                maxScale: PhotoViewComputedScale.covered * 2,
                backgroundDecoration: const BoxDecoration(color: Colors.black),
                loadingBuilder: (context, event) => Center(
                  child: CircularProgressIndicator(
                    value: event == null
                        ? 0
                        : event.cumulativeBytesLoaded /
                              event.expectedTotalBytes!,
                    valueColor: const AlwaysStoppedAnimation<Color>(
                      Colors.white,
                    ),
                  ),
                ),
                errorBuilder: (context, error, stackTrace) => const Center(
                  child: Icon(
                    Icons.broken_image,
                    color: Colors.white,
                    size: 64,
                  ),
                ),
              ),
            ),
          ),

          // Top App Bar
          AnimatedBuilder(
            animation: _buttonAnimation,
            builder: (context, child) {
              return Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Transform.translate(
                  offset: Offset(0, -60 * (1 - _buttonAnimation.value)),
                  child: Container(
                    padding: EdgeInsets.only(
                      top: MediaQuery.of(context).padding.top,
                      left: 16,
                      right: 16,
                      bottom: 16,
                    ),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.black.withValues(alpha: 0.7),
                          Colors.transparent,
                        ],
                      ),
                    ),
                    child: Row(
                      children: [
                        IconButton(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: const Icon(
                            Icons.arrow_back,
                            color: Colors.white,
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.wallpaper.title,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Row(
                                children: [
                                  Text(
                                    widget.wallpaper.category,
                                    style: const TextStyle(
                                      color: Colors.white70,
                                      fontSize: 14,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Icon(
                                    Icons.visibility,
                                    color: Colors.white70,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    '$_viewCount views',
                                    style: const TextStyle(
                                      color: Colors.white70,
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        IconButton(
                          onPressed: () {
                            // Share functionality can be added here
                          },
                          icon: const Icon(Icons.share, color: Colors.white),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),

          // Bottom Action Buttons
          AnimatedBuilder(
            animation: _buttonAnimation,
            builder: (context, child) {
              return Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  padding: EdgeInsets.only(
                    left: 16,
                    right: 16,
                    top: 24,
                    bottom: MediaQuery.of(context).padding.bottom + 16,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.8),
                      ],
                    ),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: _buildActionButton(
                              icon: Icons.download,
                              label: 'Download',
                              onPressed: _isLoading ? null : _downloadWallpaper,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildActionButton(
                              icon: Icons.hd,
                              label: 'HD Download',
                              onPressed: _isLoading
                                  ? null
                                  : _downloadHDWallpaper,
                              isPremium: true,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildActionButton(
                              icon: Icons.home,
                              label: 'Home Screen',
                              onPressed: _isLoading
                                  ? null
                                  : () => _setWallpaper(1),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Expanded(
                            child: _buildActionButton(
                              icon: Icons.lock,
                              label: 'Lock Screen',
                              onPressed: _isLoading
                                  ? null
                                  : () => _setWallpaper(2),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildActionButton(
                              icon: Icons.wallpaper,
                              label: 'Both Screens',
                              onPressed: _isLoading
                                  ? null
                                  : () => _setWallpaper(3),
                              isPrimary: true,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildActionButton(
                              icon: Icons.share,
                              label: 'Share',
                              onPressed: _isLoading ? null : _shareWallpaper,
                            ),
                          ),
                        ],
                      ),
                      // Admin delete button
                      Consumer<AuthProvider>(
                        builder: (context, authProvider, child) {
                          if (authProvider.isAdmin) {
                            return Column(
                              children: [
                                const SizedBox(height: 12),
                                SizedBox(
                                  width: double.infinity,
                                  child: _buildActionButton(
                                    icon: Icons.delete_forever,
                                    label: 'Delete Wallpaper (Admin)',
                                    onPressed: _isLoading
                                        ? null
                                        : _showDeleteConfirmation,
                                    isDelete: true,
                                  ),
                                ),
                              ],
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      ),
                    ],
                  ),
                ),
              );
            },
          ),

          // Loading Overlay
          if (_isLoading)
            Container(
              color: Colors.black.withValues(alpha: 0.5),
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
    bool isPrimary = false,
    bool isDelete = false,
    bool isPremium = false,
  }) {
    Color backgroundColor;
    Color foregroundColor;

    if (isDelete) {
      backgroundColor = Colors.red.withValues(alpha: 0.9);
      foregroundColor = Colors.white;
    } else if (isPremium) {
      backgroundColor = const Color(
        0xFFFFD700,
      ).withValues(alpha: 0.9); // Gold color for premium
      foregroundColor = Colors.black;
    } else if (isPrimary) {
      backgroundColor = AppTheme.primaryBlue;
      foregroundColor = Colors.white;
    } else {
      backgroundColor = Colors.white.withValues(alpha: 0.9);
      foregroundColor = Colors.black;
    }

    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor,
        foregroundColor: foregroundColor,
        padding: const EdgeInsets.symmetric(vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        elevation: 4,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            children: [
              Icon(icon, size: 24),
              if (isPremium)
                Positioned(
                  right: -2,
                  top: -2,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: const BoxDecoration(
                      color: Color(0xFFFFD700),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(Icons.star, size: 8, color: Colors.black),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Future<void> _incrementViewCount() async {
    final wallpaperProvider = Provider.of<WallpaperProvider>(
      context,
      listen: false,
    );
    await wallpaperProvider.incrementViewCount(widget.wallpaper.id);
  }

  Future<void> _loadViewCount() async {
    final wallpaperProvider = Provider.of<WallpaperProvider>(
      context,
      listen: false,
    );
    final viewCount = await wallpaperProvider.getViewCount(widget.wallpaper.id);
    if (mounted) {
      setState(() {
        _viewCount = viewCount;
      });
    }
  }
}
