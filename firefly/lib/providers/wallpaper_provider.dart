import 'package:flutter/material.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloudinary_public/cloudinary_public.dart';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'dart:async';
import '../models/wallpaper_model.dart';
import '../models/category_model.dart';
import '../config/firebase_config.dart';
import 'admin_config_provider.dart';
import '../services/admin_notification_service.dart';

class WallpaperProvider with ChangeNotifier {
  final FirebaseDatabase _database = FirebaseDatabase.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  late final CloudinaryPublic _cloudinary;
  final AdminConfigProvider _adminConfigProvider;

  WallpaperProvider(this._adminConfigProvider) {
    _cloudinary = CloudinaryPublic(
      FirebaseConfig.cloudinaryCloudName,
      FirebaseConfig.cloudinaryUploadPreset,
      cache: false,
    );
    // Don't load wallpapers immediately - wait for explicit call
    _loadFavorites();
    // Real-time listener will be setup after initial load in loadWallpapers()
  }

  List<WallpaperModel> _wallpapers = [];
  List<CategoryModel> _categories = [];
  String? _selectedCategory;
  String _searchQuery = '';
  String _sortOrder = 'newest'; // newest, oldest, alphabetical
  bool _isLoading = false;
  String? _errorMessage;
  Set<String> _favoriteWallpaperIds = {};
  bool _hasSetupListener = false;
  StreamSubscription<DatabaseEvent>? _wallpaperSubscription;

  List<WallpaperModel> get wallpapers {
    List<WallpaperModel> filteredWallpapers = _wallpapers;

    // Filter by category
    if (_selectedCategory != null && _selectedCategory != 'All') {
      filteredWallpapers = filteredWallpapers
          .where((w) => w.category == _selectedCategory)
          .toList();
    }

    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      filteredWallpapers = filteredWallpapers
          .where(
            (w) =>
                w.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                w.category.toLowerCase().contains(_searchQuery.toLowerCase()),
          )
          .toList();
    }

    // Sort wallpapers
    switch (_sortOrder) {
      case 'oldest':
        filteredWallpapers.sort((a, b) => a.timestamp.compareTo(b.timestamp));
        break;
      case 'alphabetical':
        filteredWallpapers.sort(
          (a, b) => a.title.toLowerCase().compareTo(b.title.toLowerCase()),
        );
        break;
      case 'newest':
      default:
        filteredWallpapers.sort((a, b) => b.timestamp.compareTo(a.timestamp));
        break;
    }

    return filteredWallpapers;
  }

  List<CategoryModel> get categories => _categories;
  String? get selectedCategory => _selectedCategory;
  String get searchQuery => _searchQuery;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  Set<String> get favoriteWallpaperIds => _favoriteWallpaperIds;

  List<WallpaperModel> get favoriteWallpapers {
    return _wallpapers
        .where((w) => _favoriteWallpaperIds.contains(w.id))
        .toList();
  }

  bool isFavorite(String wallpaperId) {
    return _favoriteWallpaperIds.contains(wallpaperId);
  }

  Future<void> loadWallpapers() async {
    // Prevent multiple simultaneous loads
    if (_isLoading) {
      print('📊 Already loading wallpapers, skipping...');
      return;
    }

    Timer? loadingTimeout;
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // Add timeout to prevent stuck loading state
      loadingTimeout = Timer(const Duration(seconds: 30), () {
        if (_isLoading) {
          print('⚠️ Loading timeout reached, resetting loading state');
          _isLoading = false;
          notifyListeners();
        }
      });

      print('🔄 Loading wallpapers from Firebase...');
      print('🔗 Database URL: ${_database.app.options.databaseURL}');
      print('🔗 Wallpapers node: ${FirebaseConfig.wallpapersNode}');

      final snapshot = await _database.ref(FirebaseConfig.wallpapersNode).get();

      print('📊 Snapshot exists: ${snapshot.exists}');
      print('📊 Snapshot value type: ${snapshot.value.runtimeType}');

      if (snapshot.exists) {
        final data = Map<String, dynamic>.from(snapshot.value as Map);
        print('📊 Found ${data.length} wallpapers in Firebase');
        print('📊 Sample data keys: ${data.keys.take(5).toList()}');

        _wallpapers = data.entries
            .map((entry) {
              try {
                return WallpaperModel.fromMap(
                  entry.key,
                  Map<String, dynamic>.from(entry.value),
                );
              } catch (e) {
                print('❌ Error parsing wallpaper ${entry.key}: $e');
                print('❌ Wallpaper data: ${entry.value}');
                return null;
              }
            })
            .where((wallpaper) => wallpaper != null)
            .cast<WallpaperModel>()
            .toList();

        print('📊 Parsed ${_wallpapers.length} wallpaper models');

        // Sort by timestamp (newest first)
        _wallpapers.sort((a, b) => b.timestamp.compareTo(a.timestamp));

        // Debug: Print first few wallpapers
        for (int i = 0; i < _wallpapers.length && i < 3; i++) {
          final w = _wallpapers[i];
          print('📱 Wallpaper $i: ${w.title} - ${w.category} - ${w.imageUrl}');
        }

        _extractCategories();

        // Setup real-time listener only after initial load and only once
        if (!_hasSetupListener) {
          _setupRealtimeListener();
          _hasSetupListener = true;
          print('📊 Real-time listener setup completed');
        }
      } else {
        print('⚠️ No wallpapers found in Firebase');
        print('⚠️ This could mean:');
        print('   1. No wallpapers have been uploaded yet');
        print('   2. Database connection issue');
        print('   3. Wrong database path');
        _wallpapers = [];
        _categories = [];
      }
    } catch (e) {
      print('❌ Error loading wallpapers: $e');
      _errorMessage = 'Failed to load wallpapers: ${e.toString()}';
      _wallpapers = [];
      _categories = [];
    } finally {
      loadingTimeout?.cancel();
      _isLoading = false;
      notifyListeners();
      print('📊 Wallpaper loading completed. Total: ${_wallpapers.length}');
    }
  }

  void _extractCategories() {
    final categoryNames = _wallpapers
        .map((w) => w.category)
        .where((category) => category.isNotEmpty) // Filter out empty categories
        .toSet()
        .toList();

    // Sort categories alphabetically (except 'All' which comes first)
    categoryNames.sort();

    print('📂 Extracting categories from ${_wallpapers.length} wallpapers');
    print('📂 Found categories: $categoryNames');

    _categories = [
      CategoryModel(name: 'All', count: _wallpapers.length),
      ...categoryNames.map(
        (name) => CategoryModel(
          name: name,
          count: _wallpapers.where((w) => w.category == name).length,
        ),
      ),
    ];

    print(
      '📂 Categories updated: ${_categories.map((c) => '${c.name}(${c.count})').join(', ')}',
    );

    // If a category was selected but no longer exists, reset to 'All'
    if (_selectedCategory != null &&
        _selectedCategory != 'All' &&
        !categoryNames.contains(_selectedCategory)) {
      print(
        '📂 Selected category "$_selectedCategory" no longer exists, resetting to All',
      );
      _selectedCategory = 'All';
    }
  }

  void _setupRealtimeListener() {
    print('🔄 Setting up real-time listener for wallpapers...');

    // Cancel existing subscription if any
    _wallpaperSubscription?.cancel();

    _wallpaperSubscription = _database
        .ref(FirebaseConfig.wallpapersNode)
        .onValue
        .listen(
          (DatabaseEvent event) {
            print('🔄 Real-time update received from Firebase');
            if (event.snapshot.exists) {
              _handleRealtimeUpdate(event.snapshot);
            }
          },
          onError: (error) {
            print('❌ Real-time listener error: $error');
          },
        );
  }

  void _handleRealtimeUpdate(DataSnapshot snapshot) {
    try {
      final data = Map<String, dynamic>.from(snapshot.value as Map);
      print('📊 Real-time update: ${data.length} wallpapers');

      final newWallpapers = data.entries
          .map((entry) {
            try {
              return WallpaperModel.fromMap(
                entry.key,
                Map<String, dynamic>.from(entry.value),
              );
            } catch (e) {
              print('❌ Error parsing wallpaper ${entry.key}: $e');
              return null;
            }
          })
          .where((wallpaper) => wallpaper != null)
          .cast<WallpaperModel>()
          .toList();

      // Sort by timestamp (newest first)
      newWallpapers.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      // Check if there are actually new wallpapers
      if (newWallpapers.length != _wallpapers.length ||
          !_wallpapersEqual(newWallpapers, _wallpapers)) {
        print(
          '📊 Wallpapers updated via real-time: ${newWallpapers.length} total',
        );
        _wallpapers = newWallpapers;
        _extractCategories();

        // Reset loading state if it was stuck
        if (_isLoading) {
          print('📊 Resetting loading state from real-time update');
          _isLoading = false;
        }

        notifyListeners();
      } else {
        print('📊 No changes detected in real-time update');
      }
    } catch (e) {
      print('❌ Error handling real-time update: $e');
    }
  }

  bool _wallpapersEqual(
    List<WallpaperModel> list1,
    List<WallpaperModel> list2,
  ) {
    if (list1.length != list2.length) return false;
    for (int i = 0; i < list1.length; i++) {
      if (list1[i].id != list2[i].id) return false;
    }
    return true;
  }

  void setSelectedCategory(String? category) {
    print('🏷️ Setting selected category: $category');
    _selectedCategory = category;
    final filteredCount = wallpapers.length;
    print('🏷️ Filtered wallpapers count: $filteredCount');
    notifyListeners();
  }

  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  void setSortOrder(String sortOrder) {
    _sortOrder = sortOrder;
    notifyListeners();
  }

  // Removed shuffleWallpapers method to prevent unnecessary data usage
  // and maintain consistent wallpaper order

  Future<bool> uploadWallpaper({
    required String imagePath,
    required String title,
    required String category,
    bool isPremium = false,
  }) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // Validate image format
      if (!FirebaseConfig.isValidImageFormat(imagePath)) {
        _errorMessage = 'Invalid image format. Please use JPG, PNG, or WebP.';
        return false;
      }

      // Get folder name for category
      final folderName = FirebaseConfig.getFolderForCategory(category);

      // Upload to Cloudinary
      final response = await _cloudinary.uploadFile(
        CloudinaryFile.fromFile(
          imagePath,
          folder: folderName,
          publicId:
              '${title.replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}',
        ),
      );

      // Create wallpaper model with optimized URLs
      final wallpaper = WallpaperModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: title,
        category: category,
        imageUrl: FirebaseConfig.getCloudinaryUrl(
          publicId: response.publicId,
          folder: folderName,
          isHd: false,
        ),
        hdUrl: FirebaseConfig.getCloudinaryUrl(
          publicId: response.publicId,
          folder: folderName,
          isHd: true,
        ),
        timestamp: DateTime.now().millisecondsSinceEpoch,
        isPremium: isPremium,
      );

      // Save to Firebase
      print('💾 Saving wallpaper to Firebase: ${wallpaper.id}');
      await _database
          .ref('${FirebaseConfig.wallpapersNode}/${wallpaper.id}')
          .set(wallpaper.toMap());
      print('✅ Wallpaper saved to Firebase successfully');

      // Add a small delay to ensure Firebase has processed the write
      await Future.delayed(const Duration(milliseconds: 500));

      // Force a refresh to ensure new categories are visible immediately
      print(
        '🔄 Force refreshing after upload to ensure new category is visible',
      );
      await loadWallpapers();

      print('✅ Wallpaper uploaded and categories refreshed');

      // Send notification to all users about new wallpaper
      try {
        await AdminNotificationService.sendNewWallpaperNotification(
          wallpaperTitle: title,
          category: category,
          wallpaperUrl: wallpaper.imageUrl,
        );
        print('📢 Wallpaper upload notification sent to all users');
      } catch (e) {
        print('⚠️ Failed to send wallpaper notification: $e');
        // Don't fail the upload if notification fails
      }

      return true;
    } catch (e) {
      _errorMessage = 'Failed to upload wallpaper: ${e.toString()}';
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> uploadMultipleWallpapers({
    required List<String> imagePaths,
    required String category,
    bool isPremium = false,
  }) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // Validate all image formats first
      for (final imagePath in imagePaths) {
        if (!FirebaseConfig.isValidImageFormat(imagePath)) {
          _errorMessage =
              'Invalid image format found. Please use JPG, PNG, or WebP only.';
          return false;
        }
      }

      final List<WallpaperModel> wallpapers = [];
      final folderName = FirebaseConfig.getFolderForCategory(category);

      for (int i = 0; i < imagePaths.length; i++) {
        final imagePath = imagePaths[i];
        final fileName = imagePath.split('/').last.split('.').first;
        final timestamp = DateTime.now().millisecondsSinceEpoch + i;

        // Upload to Cloudinary
        final response = await _cloudinary.uploadFile(
          CloudinaryFile.fromFile(
            imagePath,
            folder: folderName,
            publicId: '${fileName}_$timestamp',
          ),
        );

        // Create wallpaper model with optimized URLs
        final wallpaper = WallpaperModel(
          id: '${timestamp}_$i',
          title: fileName.replaceAll('_', ' '),
          category: category,
          imageUrl: FirebaseConfig.getCloudinaryUrl(
            publicId: response.publicId,
            folder: folderName,
            isHd: false,
          ),
          hdUrl: FirebaseConfig.getCloudinaryUrl(
            publicId: response.publicId,
            folder: folderName,
            isHd: true,
          ),
          timestamp: timestamp,
          isPremium: isPremium,
        );

        wallpapers.add(wallpaper);
      }

      // Save all to Firebase
      print('💾 Saving ${wallpapers.length} wallpapers to Firebase...');
      final batch = <String, Map<String, dynamic>>{};
      for (final wallpaper in wallpapers) {
        batch['${FirebaseConfig.wallpapersNode}/${wallpaper.id}'] = wallpaper
            .toMap();
        print('📝 Added to batch: ${wallpaper.id} - ${wallpaper.title}');
      }

      await _database.ref().update(batch);
      print('✅ Batch update completed successfully');

      // Add a small delay to ensure Firebase has processed the write
      await Future.delayed(const Duration(milliseconds: 1000));

      // Force a refresh to ensure new categories are visible immediately
      print(
        '🔄 Force refreshing after batch upload to ensure new category is visible',
      );
      await loadWallpapers();

      print('✅ Wallpapers uploaded and categories refreshed');

      // Verify all new wallpapers are in the list
      for (final wallpaper in wallpapers) {
        final found = _wallpapers.any((w) => w.id == wallpaper.id);
        if (found) {
          print('✅ Verified wallpaper in list: ${wallpaper.title}');
        } else {
          print('❌ Wallpaper not found in list: ${wallpaper.title}');
        }
      }

      // Send notification to all users about new wallpapers
      try {
        if (wallpapers.length == 1) {
          // Single wallpaper notification
          await AdminNotificationService.sendNewWallpaperNotification(
            wallpaperTitle: wallpapers.first.title,
            category: category,
            wallpaperUrl: wallpapers.first.imageUrl,
          );
        } else {
          // Multiple wallpapers notification
          await AdminNotificationService.sendNotificationToAll(
            title: 'New Wallpapers Added! 🎨✨',
            body:
                '${wallpapers.length} new wallpapers added to $category category',
            data: {
              'screen': 'category',
              'category_name': category,
              'wallpaper_count': wallpapers.length.toString(),
              'source': 'batch_upload',
            },
            imageUrl: wallpapers.first.imageUrl,
          );
        }
        print('📢 Wallpaper upload notification sent to all users');
      } catch (e) {
        print('⚠️ Failed to send wallpaper notification: $e');
        // Don't fail the upload if notification fails
      }

      return true;
    } catch (e) {
      _errorMessage = 'Failed to upload wallpapers: ${e.toString()}';
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Admin-only delete functionality
  Future<bool> deleteWallpaper({
    required String wallpaperId,
    required String publicId,
    required String userEmail,
  }) async {
    try {
      // Check if user is admin
      if (!_adminConfigProvider.isAdminUser(userEmail)) {
        _errorMessage = 'Unauthorized: Only admin can delete wallpapers';
        return false;
      }

      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // Delete from Cloudinary using authenticated API
      await _deleteFromCloudinary(publicId);

      // Delete from Firebase
      await _database
          .ref('${FirebaseConfig.wallpapersNode}/$wallpaperId')
          .remove();

      // Refresh wallpapers
      await loadWallpapers();

      return true;
    } catch (e) {
      _errorMessage = 'Failed to delete wallpaper: ${e.toString()}';
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> _deleteFromCloudinary(String publicId) async {
    try {
      final dio = Dio();

      // Cloudinary delete API endpoint
      final url =
          'https://api.cloudinary.com/v1_1/${FirebaseConfig.cloudinaryCloudName}/image/destroy';

      // Prepare form data
      final formData = FormData.fromMap({
        'public_id': publicId,
        'api_key': FirebaseConfig.cloudinaryApiKey,
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
      });

      // Generate signature for authentication
      final signature = _generateCloudinarySignature(
        publicId: publicId,
        timestamp: formData.fields
            .firstWhere((field) => field.key == 'timestamp')
            .value,
      );

      formData.fields.add(MapEntry('signature', signature));

      final response = await dio.post(
        url,
        data: formData,
        options: Options(headers: {'Content-Type': 'multipart/form-data'}),
      );

      if (response.statusCode != 200) {
        throw Exception(
          'Failed to delete from Cloudinary: ${response.statusMessage}',
        );
      }
    } catch (e) {
      throw Exception('Cloudinary deletion failed: ${e.toString()}');
    }
  }

  String _generateCloudinarySignature({
    required String publicId,
    required String timestamp,
  }) {
    // Create the string to sign according to Cloudinary's requirements
    final stringToSign =
        'public_id=$publicId&timestamp=$timestamp${FirebaseConfig.cloudinaryApiSecret}';

    // Generate SHA1 hash
    final bytes = utf8.encode(stringToSign);
    final digest = sha1.convert(bytes);

    return digest.toString();
  }

  // Helper method to extract public_id from Cloudinary URL
  String extractPublicIdFromUrl(String imageUrl) {
    try {
      // Extract public_id from Cloudinary URL
      // URL format: https://res.cloudinary.com/cloud_name/image/upload/transformations/folder/public_id.extension
      final uri = Uri.parse(imageUrl);
      final pathSegments = uri.pathSegments;

      // Find the upload segment and get everything after it
      final uploadIndex = pathSegments.indexOf('upload');
      if (uploadIndex == -1 || uploadIndex >= pathSegments.length - 1) {
        throw Exception('Invalid Cloudinary URL format');
      }

      // Get the last segment (filename) and remove extension
      final lastSegment = pathSegments.last;
      final publicId = lastSegment.split('.').first;

      // If there's a folder, include it
      if (pathSegments.length > uploadIndex + 2) {
        final folder = pathSegments[pathSegments.length - 2];
        return '$folder/$publicId';
      }

      return publicId;
    } catch (e) {
      throw Exception('Failed to extract public_id: ${e.toString()}');
    }
  }

  // Favorites functionality
  Future<void> _loadFavorites() async {
    try {
      final user = _auth.currentUser;
      if (user != null) {
        final doc = await _firestore
            .collection('users')
            .doc(user.uid)
            .collection('favorites')
            .get();

        _favoriteWallpaperIds = doc.docs.map((doc) => doc.id).toSet();
        notifyListeners();
      }
    } catch (e) {
      print('Error loading favorites: $e');
    }
  }

  Future<void> toggleFavorite(String wallpaperId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        // Handle anonymous user - store locally or prompt to sign in
        if (_favoriteWallpaperIds.contains(wallpaperId)) {
          _favoriteWallpaperIds.remove(wallpaperId);
        } else {
          _favoriteWallpaperIds.add(wallpaperId);
        }
        notifyListeners();
        return;
      }

      final favoriteRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('favorites')
          .doc(wallpaperId);

      if (_favoriteWallpaperIds.contains(wallpaperId)) {
        await favoriteRef.delete();
        _favoriteWallpaperIds.remove(wallpaperId);
      } else {
        await favoriteRef.set({'timestamp': FieldValue.serverTimestamp()});
        _favoriteWallpaperIds.add(wallpaperId);
      }
      notifyListeners();
    } catch (e) {
      print('Error toggling favorite: $e');
    }
  }

  // View count functionality
  Future<void> incrementViewCount(String wallpaperId) async {
    try {
      final viewRef = _firestore.collection('wallpapers').doc(wallpaperId);

      await _firestore.runTransaction((transaction) async {
        final snapshot = await transaction.get(viewRef);

        if (snapshot.exists) {
          final currentViews = snapshot.data()?['viewCount'] ?? 0;
          transaction.update(viewRef, {'viewCount': currentViews + 1});
        } else {
          transaction.set(viewRef, {'viewCount': 1});
        }
      });
    } catch (e) {
      print('Error incrementing view count: $e');
    }
  }

  Future<int> getViewCount(String wallpaperId) async {
    try {
      final doc = await _firestore
          .collection('wallpapers')
          .doc(wallpaperId)
          .get();

      if (doc.exists) {
        return doc.data()?['viewCount'] ?? 0;
      }
      return 0;
    } catch (e) {
      print('Error getting view count: $e');
      return 0;
    }
  }

  // Force refresh wallpapers and categories
  Future<void> forceRefresh() async {
    print('🔄 Force refreshing wallpapers...');
    await loadWallpapers();
  }

  @override
  void dispose() {
    _wallpaperSubscription?.cancel();
    super.dispose();
  }
}
