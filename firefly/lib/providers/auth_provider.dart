import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'admin_config_provider.dart';
import '../services/notification_service.dart';
import '../services/analytics_service.dart';
import '../services/user_management_service.dart';

class AuthProvider with ChangeNotifier {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  final AdminConfigProvider _adminConfigProvider;

  User? _user;
  bool _isLoading =
      true; // Start with loading true to wait for initial auth state
  bool _isInitialized = false;
  String? _errorMessage;

  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isLoggedIn => _user != null;
  bool get isAnonymous => _user?.isAnonymous ?? false;
  bool get isAuthenticated => _user != null && !(_user?.isAnonymous ?? true);
  bool get isAdmin => _adminConfigProvider.isAdminUser(_user?.email);

  AuthProvider(this._adminConfigProvider) {
    _initializeAuth();
  }

  void _initializeAuth() {
    // Listen to auth state changes
    _auth.authStateChanges().listen((User? user) async {
      if (kDebugMode) {
        print(
          '🔍 AUTH STATE CHANGE: User = ${user?.email ?? "null"}, isLoading = $_isLoading, isInitialized = $_isInitialized',
        );
      }
      _user = user;

      // Handle user authentication and setup
      if (user != null) {
        // Initialize user services in background without blocking UI
        _initializeUserServicesInBackground(user);

        // Mark as initialized and stop loading immediately when we have a user
        if (!_isInitialized) {
          _isInitialized = true;
          _isLoading = false;
          if (kDebugMode)
            print(
              '🔍 AUTH INITIALIZED with user: ${user.email ?? "anonymous"}',
            );
        } else {
          _isLoading = false;
        }
      } else {
        // No user - attempt anonymous sign in only if not already initialized
        if (!_isInitialized) {
          if (kDebugMode)
            print('🔐 No user found, attempting anonymous sign in...');
          await _signInAnonymously();
          // Don't set initialized here - wait for the auth state change with the anonymous user
          return; // Exit early, let the auth state change handle the rest
        }
      }

      // Always notify listeners when auth state changes
      if (kDebugMode) {
        print(
          '🔍 NOTIFYING LISTENERS: isLoggedIn = ${user != null}, isLoading = $_isLoading',
        );
      }
      notifyListeners();
    });

    // Reduce timeout to 3 seconds for faster startup
    Future.delayed(const Duration(seconds: 3), () {
      if (!_isInitialized) {
        if (kDebugMode) print('⏰ Auth timeout reached, forcing initialization');
        _isInitialized = true;
        _isLoading = false;
        notifyListeners();
      }
    });
  }

  // Initialize user services in background without blocking UI
  void _initializeUserServicesInBackground(User user) {
    Future(() async {
      try {
        // Create or update user document in Firestore
        await UserManagementService.createOrUpdateUser(user);

        // Ensure user stats exist (for existing users who might not have stats)
        await UserManagementService.ensureUserStatsExist(user.uid);

        // Update analytics user properties
        await AnalyticsService.updateUserProperties(user);

        // Subscribe to notifications
        await NotificationService.subscribeToTopic('all_users');
        if (kDebugMode) print('✅ User authenticated - services initialized');
      } catch (e) {
        if (kDebugMode) print('⚠️ Failed to initialize user services: $e');
      }
    });
  }

  // Sign in anonymously for regular users
  Future<void> _signInAnonymously() async {
    try {
      print('🔐 Attempting anonymous sign in...');
      final UserCredential userCredential = await _auth.signInAnonymously();
      print('✅ Anonymous sign in successful: ${userCredential.user?.uid}');

      // Track anonymous sign in
      await AnalyticsService.trackUserSignIn(signInMethod: 'anonymous');
    } catch (e) {
      print('❌ Anonymous sign in failed: $e');
      // Don't throw error - app should still work without auth
    }
  }

  // Public method to sign in anonymously (for manual triggering)
  Future<void> signInAnonymously() async {
    await _signInAnonymously();
  }

  Future<bool> signInWithGoogle() async {
    try {
      _setLoading(true);
      _clearError();

      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        _setLoading(false);
        return false;
      }

      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final UserCredential userCredential = await _auth.signInWithCredential(
        credential,
      );
      _user = userCredential.user;

      // Track Google sign in
      final bool isAdmin = _adminConfigProvider.isAdminUser(_user?.email);
      await AnalyticsService.trackUserSignIn(
        signInMethod: 'google',
        isAdmin: isAdmin,
      );

      // Don't set loading to false here - let the auth state change listener handle it
      // This prevents race condition where UI shows login screen briefly after successful login
      return true;
    } catch (e) {
      _setError('Failed to sign in with Google: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<void> signOut() async {
    try {
      _setLoading(true);

      // Track sign out
      await AnalyticsService.trackUserSignOut();

      await _googleSignIn.signOut();
      await _auth.signOut();
      _user = null;

      // After sign out, automatically sign in anonymously
      await _signInAnonymously();

      _setLoading(false);
    } catch (e) {
      _setError('Failed to sign out: ${e.toString()}');
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }
}
