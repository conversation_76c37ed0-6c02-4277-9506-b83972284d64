import 'package:shared_preferences/shared_preferences.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'admob_service.dart';

class PremiumAccessService {
  static const String _premiumAccessKey = 'premium_access_until';
  static const String _adFreeAccessKey = 'ad_free_access_until';
  static const String _rewardedAdsWatchedKey = 'rewarded_ads_watched_today';
  static const String _lastRewardDateKey = 'last_reward_date';

  // Premium access duration after watching rewarded ad
  static const int _premiumAccessHours = 24; // 24 hours of premium access
  static const int _adFreeAccessHours = 2; // 2 hours of ad-free experience
  static const int _maxRewardedAdsPerDay = 5; // Max 5 rewarded ads per day

  /// Check if user has premium access
  static Future<bool> hasPremiumAccess() async {
    final prefs = await SharedPreferences.getInstance();
    final premiumUntil = prefs.getInt(_premiumAccessKey) ?? 0;
    return DateTime.now().millisecondsSinceEpoch < premiumUntil;
  }

  /// Check if user has ad-free access
  static Future<bool> hasAdFreeAccess() async {
    final prefs = await SharedPreferences.getInstance();
    final adFreeUntil = prefs.getInt(_adFreeAccessKey) ?? 0;
    return DateTime.now().millisecondsSinceEpoch < adFreeUntil;
  }

  /// Get remaining premium access time in hours
  static Future<int> getRemainingPremiumHours() async {
    final prefs = await SharedPreferences.getInstance();
    final premiumUntil = prefs.getInt(_premiumAccessKey) ?? 0;
    final now = DateTime.now().millisecondsSinceEpoch;

    if (now >= premiumUntil) return 0;

    final remainingMs = premiumUntil - now;
    return (remainingMs / (1000 * 60 * 60)).ceil();
  }

  /// Get remaining ad-free access time in minutes
  static Future<int> getRemainingAdFreeMinutes() async {
    final prefs = await SharedPreferences.getInstance();
    final adFreeUntil = prefs.getInt(_adFreeAccessKey) ?? 0;
    final now = DateTime.now().millisecondsSinceEpoch;

    if (now >= adFreeUntil) return 0;

    final remainingMs = adFreeUntil - now;
    return (remainingMs / (1000 * 60)).ceil();
  }

  /// Check if user can watch more rewarded ads today
  static Future<bool> canWatchRewardedAd() async {
    final prefs = await SharedPreferences.getInstance();
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day).millisecondsSinceEpoch;
    final lastRewardDate = prefs.getInt(_lastRewardDateKey) ?? 0;

    if (lastRewardDate != today) {
      // New day, reset counter
      await prefs.setInt(_lastRewardDateKey, today);
      await prefs.setInt(_rewardedAdsWatchedKey, 0);
      return true;
    }

    final watchedToday = prefs.getInt(_rewardedAdsWatchedKey) ?? 0;
    return watchedToday < _maxRewardedAdsPerDay;
  }

  /// Get number of rewarded ads watched today
  static Future<int> getRewardedAdsWatchedToday() async {
    final prefs = await SharedPreferences.getInstance();
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day).millisecondsSinceEpoch;
    final lastRewardDate = prefs.getInt(_lastRewardDateKey) ?? 0;

    if (lastRewardDate != today) {
      return 0;
    }

    return prefs.getInt(_rewardedAdsWatchedKey) ?? 0;
  }

  /// Watch rewarded ad for premium access
  static Future<bool> watchAdForPremiumAccess() async {
    if (!(await canWatchRewardedAd())) {
      return false;
    }

    final adMobService = AdMobService();
    if (!adMobService.isRewardedAdLoaded) {
      return false;
    }

    bool rewardEarned = false;

    final success = await adMobService.showRewardedAd(
      onUserEarnedReward: (ad, reward) {
        rewardEarned = true;
      },
    );

    if (success && rewardEarned) {
      await _grantPremiumAccess();
      await _incrementRewardedAdsWatched();
      return true;
    }

    return false;
  }

  /// Watch rewarded ad for ad-free experience
  static Future<bool> watchAdForAdFreeAccess() async {
    if (!(await canWatchRewardedAd())) {
      return false;
    }

    final adMobService = AdMobService();
    if (!adMobService.isRewardedAdLoaded) {
      return false;
    }

    bool rewardEarned = false;

    final success = await adMobService.showRewardedAd(
      onUserEarnedReward: (ad, reward) {
        rewardEarned = true;
      },
    );

    if (success && rewardEarned) {
      await _grantAdFreeAccess();
      await _incrementRewardedAdsWatched();
      return true;
    }

    return false;
  }

  /// Grant premium access
  static Future<void> _grantPremiumAccess() async {
    final prefs = await SharedPreferences.getInstance();
    final now = DateTime.now();
    final premiumUntil = now.add(Duration(hours: _premiumAccessHours));
    await prefs.setInt(_premiumAccessKey, premiumUntil.millisecondsSinceEpoch);
  }

  /// Grant ad-free access
  static Future<void> _grantAdFreeAccess() async {
    final prefs = await SharedPreferences.getInstance();
    final now = DateTime.now();
    final adFreeUntil = now.add(Duration(hours: _adFreeAccessHours));
    await prefs.setInt(_adFreeAccessKey, adFreeUntil.millisecondsSinceEpoch);
  }

  /// Increment rewarded ads watched counter
  static Future<void> _incrementRewardedAdsWatched() async {
    final prefs = await SharedPreferences.getInstance();
    final watchedToday = prefs.getInt(_rewardedAdsWatchedKey) ?? 0;
    await prefs.setInt(_rewardedAdsWatchedKey, watchedToday + 1);
  }

  /// Clear all premium access (for testing or admin purposes)
  static Future<void> clearPremiumAccess() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_premiumAccessKey);
    await prefs.remove(_adFreeAccessKey);
  }

  /// Get premium access status summary
  static Future<Map<String, dynamic>> getPremiumStatus() async {
    return {
      'hasPremiumAccess': await hasPremiumAccess(),
      'hasAdFreeAccess': await hasAdFreeAccess(),
      'remainingPremiumHours': await getRemainingPremiumHours(),
      'remainingAdFreeMinutes': await getRemainingAdFreeMinutes(),
      'canWatchRewardedAd': await canWatchRewardedAd(),
      'rewardedAdsWatchedToday': await getRewardedAdsWatchedToday(),
      'maxRewardedAdsPerDay': _maxRewardedAdsPerDay,
    };
  }
}
