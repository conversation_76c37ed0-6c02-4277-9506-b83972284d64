import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

class AdMobConfigService {
  // Keys for storing AdMob configuration
  static const String _adsEnabledKey = 'ads_enabled';
  static const String _androidAppIdKey = 'android_app_id';
  static const String _iosAppIdKey = 'ios_app_id';
  static const String _androidBannerIdKey = 'android_banner_id';
  static const String _iosBannerIdKey = 'ios_banner_id';
  static const String _androidInterstitialIdKey = 'android_interstitial_id';
  static const String _iosInterstitialIdKey = 'ios_interstitial_id';
  static const String _androidRewardedIdKey = 'android_rewarded_id';
  static const String _iosRewardedIdKey = 'ios_rewarded_id';

  // Default test ad unit IDs
  static const String _defaultAndroidAppId = 'ca-app-pub-3940256099942544~3347511713';
  static const String _defaultIosAppId = 'ca-app-pub-3940256099942544~1458002511';
  static const String _defaultAndroidBannerId = 'ca-app-pub-3940256099942544/6300978111';
  static const String _defaultIosBannerId = 'ca-app-pub-3940256099942544/2934735716';
  static const String _defaultAndroidInterstitialId = 'ca-app-pub-3940256099942544/1033173712';
  static const String _defaultIosInterstitialId = 'ca-app-pub-3940256099942544/4411468910';
  static const String _defaultAndroidRewardedId = 'ca-app-pub-3940256099942544/5224354917';
  static const String _defaultIosRewardedId = 'ca-app-pub-3940256099942544/1712485313';

  /// Check if ads are enabled
  static Future<bool> areAdsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_adsEnabledKey) ?? true; // Default to enabled
  }

  /// Enable or disable ads
  static Future<void> setAdsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_adsEnabledKey, enabled);
    if (kDebugMode) {
      print('Ads ${enabled ? 'enabled' : 'disabled'}');
    }
  }

  /// Get Android App ID
  static Future<String> getAndroidAppId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_androidAppIdKey) ?? _defaultAndroidAppId;
  }

  /// Set Android App ID
  static Future<void> setAndroidAppId(String appId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_androidAppIdKey, appId);
  }

  /// Get iOS App ID
  static Future<String> getIosAppId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_iosAppIdKey) ?? _defaultIosAppId;
  }

  /// Set iOS App ID
  static Future<void> setIosAppId(String appId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_iosAppIdKey, appId);
  }

  /// Get Android Banner Ad ID
  static Future<String> getAndroidBannerId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_androidBannerIdKey) ?? _defaultAndroidBannerId;
  }

  /// Set Android Banner Ad ID
  static Future<void> setAndroidBannerId(String adId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_androidBannerIdKey, adId);
  }

  /// Get iOS Banner Ad ID
  static Future<String> getIosBannerId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_iosBannerIdKey) ?? _defaultIosBannerId;
  }

  /// Set iOS Banner Ad ID
  static Future<void> setIosBannerId(String adId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_iosBannerIdKey, adId);
  }

  /// Get Android Interstitial Ad ID
  static Future<String> getAndroidInterstitialId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_androidInterstitialIdKey) ?? _defaultAndroidInterstitialId;
  }

  /// Set Android Interstitial Ad ID
  static Future<void> setAndroidInterstitialId(String adId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_androidInterstitialIdKey, adId);
  }

  /// Get iOS Interstitial Ad ID
  static Future<String> getIosInterstitialId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_iosInterstitialIdKey) ?? _defaultIosInterstitialId;
  }

  /// Set iOS Interstitial Ad ID
  static Future<void> setIosInterstitialId(String adId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_iosInterstitialIdKey, adId);
  }

  /// Get Android Rewarded Ad ID
  static Future<String> getAndroidRewardedId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_androidRewardedIdKey) ?? _defaultAndroidRewardedId;
  }

  /// Set Android Rewarded Ad ID
  static Future<void> setAndroidRewardedId(String adId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_androidRewardedIdKey, adId);
  }

  /// Get iOS Rewarded Ad ID
  static Future<String> getIosRewardedId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_iosRewardedIdKey) ?? _defaultIosRewardedId;
  }

  /// Set iOS Rewarded Ad ID
  static Future<void> setIosRewardedId(String adId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_iosRewardedIdKey, adId);
  }

  /// Get all AdMob configuration
  static Future<Map<String, dynamic>> getAllConfig() async {
    return {
      'adsEnabled': await areAdsEnabled(),
      'androidAppId': await getAndroidAppId(),
      'iosAppId': await getIosAppId(),
      'androidBannerId': await getAndroidBannerId(),
      'iosBannerId': await getIosBannerId(),
      'androidInterstitialId': await getAndroidInterstitialId(),
      'iosInterstitialId': await getIosInterstitialId(),
      'androidRewardedId': await getAndroidRewardedId(),
      'iosRewardedId': await getIosRewardedId(),
    };
  }

  /// Save all AdMob configuration
  static Future<void> saveAllConfig(Map<String, dynamic> config) async {
    await setAdsEnabled(config['adsEnabled'] ?? true);
    await setAndroidAppId(config['androidAppId'] ?? _defaultAndroidAppId);
    await setIosAppId(config['iosAppId'] ?? _defaultIosAppId);
    await setAndroidBannerId(config['androidBannerId'] ?? _defaultAndroidBannerId);
    await setIosBannerId(config['iosBannerId'] ?? _defaultIosBannerId);
    await setAndroidInterstitialId(config['androidInterstitialId'] ?? _defaultAndroidInterstitialId);
    await setIosInterstitialId(config['iosInterstitialId'] ?? _defaultIosInterstitialId);
    await setAndroidRewardedId(config['androidRewardedId'] ?? _defaultAndroidRewardedId);
    await setIosRewardedId(config['iosRewardedId'] ?? _defaultIosRewardedId);
  }

  /// Reset to default configuration
  static Future<void> resetToDefaults() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_adsEnabledKey);
    await prefs.remove(_androidAppIdKey);
    await prefs.remove(_iosAppIdKey);
    await prefs.remove(_androidBannerIdKey);
    await prefs.remove(_iosBannerIdKey);
    await prefs.remove(_androidInterstitialIdKey);
    await prefs.remove(_iosInterstitialIdKey);
    await prefs.remove(_androidRewardedIdKey);
    await prefs.remove(_iosRewardedIdKey);
    if (kDebugMode) {
      print('AdMob configuration reset to defaults');
    }
  }

  /// Validate ad unit ID format
  static bool isValidAdUnitId(String adUnitId) {
    if (adUnitId.isEmpty) return false;
    // Basic validation for AdMob ad unit ID format
    final regex = RegExp(r'^ca-app-pub-[0-9]{16}[~/][0-9]{10}$');
    return regex.hasMatch(adUnitId);
  }

  /// Get current configuration status
  static Future<String> getConfigStatus() async {
    final config = await getAllConfig();
    final adsEnabled = config['adsEnabled'] as bool;
    final androidAppId = config['androidAppId'] as String;
    final iosAppId = config['iosAppId'] as String;
    
    if (!adsEnabled) {
      return 'Ads Disabled';
    }
    
    final isUsingDefaults = androidAppId == _defaultAndroidAppId && 
                           iosAppId == _defaultIosAppId;
    
    if (isUsingDefaults) {
      return 'Using Test Ads';
    }
    
    return 'Using Production Ads';
  }
}
