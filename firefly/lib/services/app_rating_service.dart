import 'package:flutter/material.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:io';
import '../services/analytics_service.dart';

class AppRatingService {
  static const String _ratingPromptShownKey = 'rating_prompt_shown';
  static const String _lastRatingPromptKey = 'last_rating_prompt';
  static const String _appLaunchCountKey = 'app_launch_count';
  static const String _wallpapersDownloadedKey = 'wallpapers_downloaded_count';
  static const String _userRatedAppKey = 'user_rated_app';
  static const String _neverShowRatingKey = 'never_show_rating';

  // Thresholds for showing rating prompts
  static const int minLaunchesForRating = 5;
  static const int minDownloadsForRating = 3;
  static const int daysBetweenPrompts = 30;

  /// Check if we should show a rating prompt based on user engagement
  static Future<bool> shouldShowRatingPrompt() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Don't show if user already rated or chose never to rate
      if (prefs.getBool(_userRatedAppKey) == true ||
          prefs.getBool(_neverShowRatingKey) == true) {
        return false;
      }

      // Check if enough time has passed since last prompt
      final lastPrompt = prefs.getInt(_lastRatingPromptKey) ?? 0;
      final daysSinceLastPrompt = DateTime.now()
          .difference(DateTime.fromMillisecondsSinceEpoch(lastPrompt))
          .inDays;

      if (lastPrompt > 0 && daysSinceLastPrompt < daysBetweenPrompts) {
        return false;
      }

      // Check user engagement metrics
      final launchCount = prefs.getInt(_appLaunchCountKey) ?? 0;
      final downloadCount = prefs.getInt(_wallpapersDownloadedKey) ?? 0;

      return launchCount >= minLaunchesForRating ||
          downloadCount >= minDownloadsForRating;
    } catch (e) {
      print('❌ Error checking rating prompt eligibility: $e');
      return false;
    }
  }

  /// Increment app launch count
  static Future<void> incrementAppLaunchCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentCount = prefs.getInt(_appLaunchCountKey) ?? 0;
      await prefs.setInt(_appLaunchCountKey, currentCount + 1);
      print('📱 App launch count: ${currentCount + 1}');
    } catch (e) {
      print('❌ Error incrementing app launch count: $e');
    }
  }

  /// Increment wallpaper download count
  static Future<void> incrementDownloadCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentCount = prefs.getInt(_wallpapersDownloadedKey) ?? 0;
      await prefs.setInt(_wallpapersDownloadedKey, currentCount + 1);
      print('📥 Download count: ${currentCount + 1}');
    } catch (e) {
      print('❌ Error incrementing download count: $e');
    }
  }

  /// Show in-app rating dialog
  static Future<void> showInAppRating() async {
    try {
      final InAppReview inAppReview = InAppReview.instance;

      if (await inAppReview.isAvailable()) {
        await inAppReview.requestReview();
        await _markRatingPromptShown();
        await _markUserRatedApp();

        // Store rating action in database (we don't know the actual rating value for in-app review)
        await _storeRatingAction('in_app_review', null);

        // Track analytics
        await AnalyticsService.trackAppRating(
          action: 'rated',
          ratingType: 'in_app_review',
          trigger: 'manual',
        );

        print('✅ In-app rating dialog shown');
      } else {
        // Fallback to Play Store
        await openPlayStore();
      }
    } catch (e) {
      print('❌ Error showing in-app rating: $e');
      // Fallback to Play Store
      await openPlayStore();
    }
  }

  /// Open Play Store for rating
  static Future<void> openPlayStore() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final packageName = packageInfo.packageName;

      String url;
      if (Platform.isAndroid) {
        url = 'https://play.google.com/store/apps/details?id=$packageName';
      } else if (Platform.isIOS) {
        // You'll need to replace with your actual App Store ID
        url =
            'https://apps.apple.com/app/id1234567890'; // Replace with actual ID
      } else {
        url = 'https://play.google.com/store/apps/details?id=$packageName';
      }

      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        await _markRatingPromptShown();
        await _markUserRatedApp();

        // Store rating action in database
        await _storeRatingAction('play_store', null);

        // Track analytics
        await AnalyticsService.trackAppRating(
          action: 'rated',
          ratingType: 'play_store',
          trigger: 'manual',
        );

        print('✅ Play Store opened for rating');
      } else {
        throw 'Could not launch $url';
      }
    } catch (e) {
      print('❌ Error opening Play Store: $e');
    }
  }

  /// Share app with others
  static Future<void> shareApp({String? customMessage}) async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final appName = packageInfo.appName;
      final packageName = packageInfo.packageName;

      String playStoreUrl;
      if (Platform.isAndroid) {
        playStoreUrl =
            'https://play.google.com/store/apps/details?id=$packageName';
      } else if (Platform.isIOS) {
        // Replace with your actual App Store ID
        playStoreUrl = 'https://apps.apple.com/app/id1234567890';
      } else {
        playStoreUrl =
            'https://play.google.com/store/apps/details?id=$packageName';
      }

      final message =
          customMessage ??
          'Check out $appName - Amazing 4K HD Wallpapers! 🎨✨\n\n'
              'Download beautiful wallpapers for your phone:\n'
              '$playStoreUrl\n\n'
              '#Wallpapers #4K #HD #FireFly';

      await Share.share(message, subject: 'Check out $appName!');

      // Track analytics
      await AnalyticsService.trackAppSharing(
        action: 'app_shared',
        shareMethod: 'native_share',
        source: 'manual',
      );

      print('✅ App shared successfully');
    } catch (e) {
      print('❌ Error sharing app: $e');
    }
  }

  /// Share specific wallpaper
  static Future<void> shareWallpaper({
    required String wallpaperTitle,
    required String wallpaperUrl,
    String? customMessage,
  }) async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final appName = packageInfo.appName;
      final packageName = packageInfo.packageName;

      String playStoreUrl;
      if (Platform.isAndroid) {
        playStoreUrl =
            'https://play.google.com/store/apps/details?id=$packageName';
      } else {
        playStoreUrl = 'https://apps.apple.com/app/id1234567890';
      }

      final message =
          customMessage ??
          'Check out this amazing wallpaper "$wallpaperTitle" from $appName! 🎨\n\n'
              'Download more beautiful 4K HD wallpapers:\n'
              '$playStoreUrl\n\n'
              '#Wallpapers #4K #HD #FireFly';

      await Share.share(message, subject: 'Amazing Wallpaper from $appName');

      // Track analytics
      await AnalyticsService.trackAppSharing(
        action: 'wallpaper_shared',
        shareMethod: 'native_share',
        source: 'manual',
        wallpaperTitle: wallpaperTitle,
      );

      print('✅ Wallpaper shared successfully: $wallpaperTitle');
    } catch (e) {
      print('❌ Error sharing wallpaper: $e');
    }
  }

  /// Mark that rating prompt was shown
  static Future<void> _markRatingPromptShown() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_ratingPromptShownKey, true);
      await prefs.setInt(
        _lastRatingPromptKey,
        DateTime.now().millisecondsSinceEpoch,
      );
    } catch (e) {
      print('❌ Error marking rating prompt shown: $e');
    }
  }

  /// Mark that user rated the app
  static Future<void> _markUserRatedApp() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_userRatedAppKey, true);
    } catch (e) {
      print('❌ Error marking user rated app: $e');
    }
  }

  /// Mark that user chose never to rate
  static Future<void> markNeverShowRating() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_neverShowRatingKey, true);

      // Store rating action in database
      await _storeRatingAction('never_ask', null);

      // Track analytics
      await AnalyticsService.trackAppRating(
        action: 'never_ask',
        trigger: 'user_choice',
      );

      print('✅ User chose never to show rating prompt');
    } catch (e) {
      print('❌ Error marking never show rating: $e');
    }
  }

  /// Store rating action in database
  static Future<void> _storeRatingAction(
    String ratingType,
    int? ratingValue,
  ) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        print('⚠️ No authenticated user, cannot store rating action');
        return;
      }

      // Temporarily disable Firestore operations due to permission issues
      print(
        '📊 Rating action logged locally: $ratingType (value: $ratingValue)',
      );
    } catch (e) {
      print('❌ Error storing rating action in database: $e');
    }
  }

  /// Reset rating preferences (for testing)
  static Future<void> resetRatingPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_ratingPromptShownKey);
      await prefs.remove(_lastRatingPromptKey);
      await prefs.remove(_userRatedAppKey);
      await prefs.remove(_neverShowRatingKey);
      await prefs.remove(_appLaunchCountKey);
      await prefs.remove(_wallpapersDownloadedKey);
      print('✅ Rating preferences reset');
    } catch (e) {
      print('❌ Error resetting rating preferences: $e');
    }
  }

  /// Get current engagement stats
  static Future<Map<String, int>> getEngagementStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return {
        'launches': prefs.getInt(_appLaunchCountKey) ?? 0,
        'downloads': prefs.getInt(_wallpapersDownloadedKey) ?? 0,
      };
    } catch (e) {
      print('❌ Error getting engagement stats: $e');
      return {'launches': 0, 'downloads': 0};
    }
  }
}
