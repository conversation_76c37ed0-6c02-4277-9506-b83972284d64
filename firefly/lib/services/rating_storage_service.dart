import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class RatingStorageService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Get all app ratings from database
  static Future<List<Map<String, dynamic>>> getAllRatings() async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('app_ratings')
          .orderBy('timestamp', descending: true)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('❌ Error getting all ratings: $e');
      return [];
    }
  }

  /// Get ratings for a specific user
  static Future<List<Map<String, dynamic>>> getUserRatings(
    String userId,
  ) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('app_ratings')
          .where('userId', isEqualTo: userId)
          .orderBy('timestamp', descending: true)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('❌ Error getting user ratings: $e');
      return [];
    }
  }

  /// Get current user's ratings
  static Future<List<Map<String, dynamic>>> getCurrentUserRatings() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        print('⚠️ No authenticated user');
        return [];
      }

      return await getUserRatings(user.uid);
    } catch (e) {
      print('❌ Error getting current user ratings: $e');
      return [];
    }
  }

  /// Get rating statistics
  static Future<Map<String, dynamic>> getRatingStatistics() async {
    try {
      // Get all ratings without complex where clause to avoid permission issues
      final QuerySnapshot snapshot = await _firestore
          .collection('app_ratings')
          .get();

      final Map<int, int> ratingCounts = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0};
      int totalRatings = 0;
      double totalScore = 0;

      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final ratingValue = data['ratingValue'] as int?;

        if (ratingValue != null && ratingValue >= 1 && ratingValue <= 5) {
          ratingCounts[ratingValue] = (ratingCounts[ratingValue] ?? 0) + 1;
          totalRatings++;
          totalScore += ratingValue;
        }
      }

      final double averageRating = totalRatings > 0
          ? totalScore / totalRatings
          : 0;

      return {
        'totalRatings': totalRatings,
        'averageRating': averageRating,
        'ratingCounts': ratingCounts,
        'fiveStarPercentage': totalRatings > 0
            ? (ratingCounts[5]! / totalRatings * 100)
            : 0,
        'fourPlusStarPercentage': totalRatings > 0
            ? ((ratingCounts[4]! + ratingCounts[5]!) / totalRatings * 100)
            : 0,
      };
    } catch (e) {
      print('❌ Error getting rating statistics: $e');
      return {
        'totalRatings': 0,
        'averageRating': 0.0,
        'ratingCounts': {1: 0, 2: 0, 3: 0, 4: 0, 5: 0},
        'fiveStarPercentage': 0.0,
        'fourPlusStarPercentage': 0.0,
      };
    }
  }

  /// Get rating actions (including non-star ratings like 'never_ask', 'play_store')
  static Future<Map<String, int>> getRatingActions() async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('app_ratings')
          .get();

      final Map<String, int> actionCounts = {};

      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final ratingType = data['ratingType'] as String?;

        if (ratingType != null) {
          actionCounts[ratingType] = (actionCounts[ratingType] ?? 0) + 1;
        }
      }

      return actionCounts;
    } catch (e) {
      print('❌ Error getting rating actions: $e');
      return {};
    }
  }

  /// Check if current user has already rated
  static Future<bool> hasUserRated() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return false;

      final QuerySnapshot snapshot = await _firestore
          .collection('app_ratings')
          .where('userId', isEqualTo: user.uid)
          .limit(1)
          .get();

      return snapshot.docs.isNotEmpty;
    } catch (e) {
      print('❌ Error checking if user has rated: $e');
      return false;
    }
  }

  /// Get user's latest rating
  static Future<Map<String, dynamic>?> getUserLatestRating() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return null;

      final QuerySnapshot snapshot = await _firestore
          .collection('app_ratings')
          .where('userId', isEqualTo: user.uid)
          .orderBy('timestamp', descending: true)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        final data = snapshot.docs.first.data() as Map<String, dynamic>;
        data['id'] = snapshot.docs.first.id;
        return data;
      }

      return null;
    } catch (e) {
      print('❌ Error getting user latest rating: $e');
      return null;
    }
  }

  /// Delete a rating (admin function)
  static Future<bool> deleteRating(String ratingId) async {
    try {
      await _firestore.collection('app_ratings').doc(ratingId).delete();
      print('✅ Rating deleted: $ratingId');
      return true;
    } catch (e) {
      print('❌ Error deleting rating: $e');
      return false;
    }
  }

  /// Get ratings by date range
  static Future<List<Map<String, dynamic>>> getRatingsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('app_ratings')
          .where(
            'timestamp',
            isGreaterThanOrEqualTo: Timestamp.fromDate(startDate),
          )
          .where('timestamp', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
          .orderBy('timestamp', descending: true)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('❌ Error getting ratings by date range: $e');
      return [];
    }
  }

  /// Get recent ratings (last N days)
  static Future<List<Map<String, dynamic>>> getRecentRatings({
    int days = 7,
  }) async {
    try {
      final DateTime startDate = DateTime.now().subtract(Duration(days: days));
      return await getRatingsByDateRange(startDate, DateTime.now());
    } catch (e) {
      print('❌ Error getting recent ratings: $e');
      return [];
    }
  }
}
