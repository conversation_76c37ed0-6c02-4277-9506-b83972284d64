import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../widgets/notification_widget.dart';
import 'notification_listener_service.dart';

class NotificationService {
  static final FirebaseMessaging _firebaseMessaging =
      FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static String? _fcmToken;
  static const String _channelId = 'firefly_notifications';
  static const String _channelName = 'FireFly Notifications';
  static const String _channelDescription =
      'Notifications for new wallpapers and updates';

  // Track processed messages to prevent duplicates
  static final List<String> _processedMessages = [];

  // Initialize Firebase Cloud Messaging and Local Notifications
  static Future<void> initialize() async {
    try {
      print('=== NOTIFICATION SERVICE INITIALIZATION START ===');

      // Initialize local notifications first
      await _initializeLocalNotifications();
      print('✅ Local notifications initialized');

      // Request runtime notification permissions for Android 13+
      bool hasRuntimePermission = await _requestRuntimeNotificationPermission();
      if (!hasRuntimePermission) {
        print('❌ Runtime notification permission denied');
        return;
      }

      // Request notification permissions
      print('🔔 Requesting Firebase notification permissions...');
      NotificationSettings settings = await _firebaseMessaging
          .requestPermission(
            alert: true,
            announcement: false,
            badge: true,
            carPlay: false,
            criticalAlert: false,
            provisional: false,
            sound: true,
          );

      print('📱 Permission status: ${settings.authorizationStatus}');
      print('📱 Alert setting: ${settings.alert}');
      print('📱 Badge setting: ${settings.badge}');
      print('📱 Sound setting: ${settings.sound}');

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        print('✅ User granted permission');

        // Get FCM token with retry mechanism
        await _getFCMTokenWithRetry();

        // Save FCM token to Firestore
        if (_fcmToken != null) {
          await _saveFCMTokenToFirestore(_fcmToken!);
          await subscribeToTopic('all_users');
          print('✅ Successfully subscribed to all_users topic');
        } else {
          print('❌ ERROR: FCM token is null, cannot subscribe to topic');
        }

        // Configure foreground notification presentation options
        await _firebaseMessaging.setForegroundNotificationPresentationOptions(
          alert: true,
          badge: true,
          sound: true,
        );
        print('✅ Foreground notification options configured');

        // Set up message handlers
        _setupMessageHandlers();
        print('✅ Message handlers configured');

        // Set up token refresh
        setupTokenRefresh();
        print('✅ Token refresh listener configured');
      } else if (settings.authorizationStatus ==
          AuthorizationStatus.provisional) {
        print('⚠️ User granted provisional permission');
      } else {
        print('❌ User declined or has not accepted permission');
      }

      // Start periodic token refresh
      schedulePeriodicTokenRefresh();

      // Restart notification services (in case app was killed)
      await restartNotificationServices();

      print('=== NOTIFICATION SERVICE INITIALIZATION COMPLETE ===');
    } catch (e, stackTrace) {
      print('❌ Error initializing notifications: $e');
      print('Stack trace: $stackTrace');
      // Don't rethrow to prevent app crash
    }
  }

  // Public method to get FCM token
  static Future<String?> getFCMToken() async {
    if (_fcmToken != null) {
      return _fcmToken;
    }

    try {
      _fcmToken = await _firebaseMessaging.getToken();
      return _fcmToken;
    } catch (e) {
      print('❌ Error getting FCM token: $e');
      return null;
    }
  }

  // Public method to check notification permission status
  static Future<bool> hasNotificationPermission() async {
    try {
      if (!Platform.isAndroid) {
        // For iOS, check Firebase permission
        final settings = await _firebaseMessaging.getNotificationSettings();
        return settings.authorizationStatus == AuthorizationStatus.authorized;
      }

      // For Android, check both runtime and Firebase permissions
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;

      if (androidInfo.version.sdkInt >= 33) {
        // Android 13+ requires runtime permission
        final runtimeStatus = await Permission.notification.status;
        if (!runtimeStatus.isGranted) {
          return false;
        }
      }

      // Check Firebase permission
      final settings = await _firebaseMessaging.getNotificationSettings();
      return settings.authorizationStatus == AuthorizationStatus.authorized;
    } catch (e) {
      print('❌ Error checking notification permission: $e');
      return false;
    }
  }

  // Public method to request notification permissions (can be called from UI)
  static Future<bool> requestNotificationPermission() async {
    try {
      print('🔔 Requesting notification permissions from UI...');

      // First request runtime permission for Android 13+
      bool hasRuntimePermission = await _requestRuntimeNotificationPermission();
      if (!hasRuntimePermission) {
        return false;
      }

      // Then request Firebase permission
      NotificationSettings settings = await _firebaseMessaging
          .requestPermission(
            alert: true,
            announcement: false,
            badge: true,
            carPlay: false,
            criticalAlert: false,
            provisional: false,
            sound: true,
          );

      return settings.authorizationStatus == AuthorizationStatus.authorized;
    } catch (e) {
      print('❌ Error requesting notification permission: $e');
      return false;
    }
  }

  // Check if app has necessary permissions for background notifications
  static Future<bool> hasBackgroundNotificationPermissions() async {
    try {
      if (!Platform.isAndroid) {
        return true; // iOS handles this differently
      }

      // Check notification permission
      bool hasNotificationPerm = await hasNotificationPermission();
      if (!hasNotificationPerm) {
        print('❌ Missing notification permission');
        return false;
      }

      print('✅ All background notification permissions granted');
      return true;
    } catch (e) {
      print('❌ Error checking background notification permissions: $e');
      return false;
    }
  }

  // Request runtime notification permission for Android 13+
  static Future<bool> _requestRuntimeNotificationPermission() async {
    try {
      // Check if we're on Android
      if (!Platform.isAndroid) {
        print('✅ Not Android - runtime permission not needed');
        return true;
      }

      // Get Android version
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      final sdkInt = androidInfo.version.sdkInt;

      print('📱 Android SDK version: $sdkInt');

      // Check if Android 13+ (API 33+)
      if (sdkInt >= 33) {
        print(
          '🔔 Android 13+ detected - requesting POST_NOTIFICATIONS permission',
        );

        // Check current permission status
        final status = await Permission.notification.status;
        print('📱 Current notification permission status: $status');

        if (status.isGranted) {
          print('✅ POST_NOTIFICATIONS permission already granted');
          return true;
        }

        if (status.isPermanentlyDenied) {
          print('❌ POST_NOTIFICATIONS permission permanently denied');
          print('💡 User needs to enable notifications in device settings');
          return false;
        }

        // Request permission
        print('🔔 Requesting POST_NOTIFICATIONS permission...');
        final result = await Permission.notification.request();
        print('📱 Permission request result: $result');

        if (result.isGranted) {
          print('✅ POST_NOTIFICATIONS permission granted');
          return true;
        } else {
          print('❌ POST_NOTIFICATIONS permission denied');
          return false;
        }
      } else {
        print('✅ Android < 13 - runtime notification permission not required');
        return true;
      }
    } catch (e) {
      print('❌ Error requesting runtime notification permission: $e');
      // Return true to not block initialization on older devices
      return true;
    }
  }

  // Get FCM token with retry mechanism
  static Future<void> _getFCMTokenWithRetry() async {
    int retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        _fcmToken = await _firebaseMessaging.getToken();
        if (_fcmToken != null) {
          print('✅ FCM Token obtained: ${_fcmToken!.substring(0, 20)}...');
          return;
        } else {
          print(
            '⚠️ FCM Token is null, retrying... (${retryCount + 1}/$maxRetries)',
          );
        }
      } catch (e) {
        print('❌ Error getting FCM token (attempt ${retryCount + 1}): $e');
      }

      retryCount++;
      if (retryCount < maxRetries) {
        await Future.delayed(Duration(seconds: retryCount * 2));
      }
    }

    print('❌ Failed to get FCM token after $maxRetries attempts');
  }

  // Initialize local notifications
  static Future<void> _initializeLocalNotifications() async {
    try {
      print('🔔 Initializing local notifications...');

      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@drawable/ic_notification');

      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
            requestAlertPermission: true,
            requestBadgePermission: true,
            requestSoundPermission: true,
          );

      const InitializationSettings initializationSettings =
          InitializationSettings(
            android: initializationSettingsAndroid,
            iOS: initializationSettingsIOS,
          );

      final bool? initialized = await _localNotifications.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      if (initialized == true) {
        print('✅ Local notifications initialized successfully');
      } else {
        print('⚠️ Local notifications initialization returned false');
      }

      // Create notification channel for Android
      if (Platform.isAndroid) {
        const AndroidNotificationChannel channel = AndroidNotificationChannel(
          _channelId,
          _channelName,
          description: _channelDescription,
          importance: Importance.high,
          enableVibration: true,
          playSound: true,
          showBadge: true,
        );

        final androidPlugin = _localNotifications
            .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin
            >();

        if (androidPlugin != null) {
          await androidPlugin.createNotificationChannel(channel);
          print('✅ Android notification channel created: $_channelId');

          // Check if notifications are enabled
          final bool? areNotificationsEnabled = await androidPlugin
              .areNotificationsEnabled();
          print('📱 Android notifications enabled: $areNotificationsEnabled');
        } else {
          print('❌ Could not get Android notification plugin');
        }
      }

      print('✅ Local notifications setup completed');
    } catch (e, stackTrace) {
      print('❌ Error initializing local notifications: $e');
      print('Stack trace: $stackTrace');
      // Don't rethrow to prevent app crash
    }
  }

  // Handle notification tap
  static void _onNotificationTapped(NotificationResponse response) {
    print('Notification tapped: ${response.payload}');
    if (response.payload != null) {
      try {
        final data = jsonDecode(response.payload!);

        // Add delay to ensure app is fully initialized and context is set
        // This prevents login screen from showing when user is already authenticated
        Future.delayed(const Duration(milliseconds: 1500), () {
          _navigateToScreen(data['screen'] ?? 'home', data);
        });
      } catch (e) {
        print('Error parsing notification payload: $e');
      }
    }
  }

  // Set up message handlers for different states
  static void _setupMessageHandlers() {
    try {
      // Handle messages when app is in foreground ONLY
      // This prevents duplicate notifications with background handler
      FirebaseMessaging.onMessage.listen(
        (RemoteMessage message) {
          print('📨 Received foreground message: ${message.messageId}');
          print('📨 From: ${message.from}');
          print('📨 Data: ${message.data}');
          print('📨 Notification: ${message.notification?.title}');
          print('🔍 App is in foreground - handling notification manually');
          _handleMessage(message);
        },
        onError: (error) {
          print('❌ Error in foreground message listener: $error');
        },
      );

      // Handle messages when app is in background but not terminated
      FirebaseMessaging.onMessageOpenedApp.listen(
        (RemoteMessage message) {
          print('📨 Message clicked from background: ${message.messageId}');
          print('📨 Data: ${message.data}');
          _handleMessageClick(message);
        },
        onError: (error) {
          print('❌ Error in background message listener: $error');
        },
      );

      // Handle messages when app is terminated
      FirebaseMessaging.instance
          .getInitialMessage()
          .then((RemoteMessage? message) {
            if (message != null) {
              print(
                '📨 Message clicked from terminated state: ${message.messageId}',
              );
              print('📨 Data: ${message.data}');
              _handleMessageClick(message);
            } else {
              print('📨 No initial message found');
            }
          })
          .catchError((error) {
            print('❌ Error getting initial message: $error');
          });

      print('✅ All message handlers set up successfully');
    } catch (e) {
      print('❌ Error setting up message handlers: $e');
    }
  }

  // Handle incoming messages
  static void _handleMessage(RemoteMessage message) {
    try {
      print('🔄 Processing incoming FCM message...');
      print('📨 Message ID: ${message.messageId}');
      print('📨 Message data: ${message.data}');
      print('📨 Message from: ${message.from}');
      print('📨 Message sent time: ${message.sentTime}');

      // Check if message was already processed to prevent duplicates
      String messageKey =
          '${message.messageId}_${message.sentTime?.millisecondsSinceEpoch}';
      if (_processedMessages.contains(messageKey)) {
        print('⚠️ Message already processed, skipping: ${message.messageId}');
        return;
      }
      _processedMessages.add(messageKey);

      // Clean up old processed messages (keep only last 50)
      if (_processedMessages.length > 50) {
        _processedMessages.removeRange(0, _processedMessages.length - 50);
      }

      // Temporarily disable NotificationListenerService to prevent duplicates
      NotificationListenerService.setEnabled(false);

      if (message.notification != null) {
        print('📨 Message notification title: ${message.notification!.title}');
        print('📨 Message notification body: ${message.notification!.body}');
        print(
          '📨 Message notification image: ${message.notification!.android?.imageUrl ?? message.notification!.apple?.imageUrl}',
        );

        // Show local notification
        showLocalNotification(
              title: message.notification!.title ?? 'FireFly',
              body: message.notification!.body ?? 'New notification',
              payload: jsonEncode(message.data),
              imageUrl:
                  message.notification!.android?.imageUrl ??
                  message.notification!.apple?.imageUrl,
            )
            .then((_) {
              print('✅ FCM notification shown successfully');
              // Re-enable NotificationListenerService after a delay
              Future.delayed(const Duration(seconds: 5), () {
                NotificationListenerService.setEnabled(true);
              });
            })
            .catchError((error) {
              print('❌ Error showing FCM notification: $error');
              // Re-enable NotificationListenerService even on error
              NotificationListenerService.setEnabled(true);
            });

        // Show in-app notification if context is available
        try {
          _showInAppNotification(message);
          print('✅ In-app notification processed');
        } catch (e) {
          print('❌ Error showing in-app notification: $e');
        }
      } else {
        print('⚠️ Message has no notification payload, only data');
        // Handle data-only messages
        if (message.data.isNotEmpty) {
          print('📨 Processing data-only message: ${message.data}');
          // You can add custom logic here for data-only messages
        }
        // Re-enable NotificationListenerService for data-only messages
        NotificationListenerService.setEnabled(true);
      }
    } catch (e, stackTrace) {
      print('❌ Error handling FCM message: $e');
      print('Stack trace: $stackTrace');
      // Re-enable NotificationListenerService on error
      NotificationListenerService.setEnabled(true);
    }
  }

  // Test function for release build notifications
  static Future<void> testReleaseNotification() async {
    try {
      print('Testing release notification...');
      await showLocalNotification(
        title: 'Test Notification',
        body: 'This is a test notification for release build',
        payload: 'test_payload',
      );
      print('Test notification sent successfully');
    } catch (e) {
      print('ERROR in test notification: $e');
    }
  }

  // Show local notification
  static Future<void> showLocalNotification({
    required String title,
    required String body,
    String? payload,
    String? imageUrl,
  }) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
          _channelId,
          _channelName,
          channelDescription: _channelDescription,
          importance: Importance.high,
          priority: Priority.high,
          icon: '@drawable/ic_notification',
          enableVibration: true,
          playSound: true,
        );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _localNotifications.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      platformChannelSpecifics,
      payload: payload,
    );
  }

  // Handle message clicks
  static void _handleMessageClick(RemoteMessage message) {
    print('Message clicked: ${message.data}');

    // Navigate to specific screen based on message data
    if (message.data.containsKey('screen')) {
      String screen = message.data['screen'];

      // Add delay to ensure app is fully initialized and context is set
      // This is especially important when app is launched from terminated state
      Future.delayed(const Duration(milliseconds: 1500), () {
        _navigateToScreen(screen, message.data);
      });
    }
  }

  // Show in-app notification
  static void _showInAppNotification(RemoteMessage message) {
    if (_currentContext != null && message.notification != null) {
      NotificationWidget.showSnackBar(
        context: _currentContext!,
        title: message.notification!.title ?? 'FireFly',
        body: message.notification!.body ?? 'New notification',
        icon: Icons.wallpaper,
        onTap: () {
          _handleMessageClick(message);
        },
      );
    }
  }

  // Store current context for showing notifications
  static BuildContext? _currentContext;

  static void setContext(BuildContext context) {
    _currentContext = context;
  }

  // Navigate to specific screen based on notification data
  static void _navigateToScreen(String screen, Map<String, dynamic> data) {
    if (_currentContext == null) return;

    // Implement navigation logic based on screen parameter
    switch (screen) {
      case 'wallpaper':
        // Navigate to specific wallpaper
        if (data.containsKey('wallpaper_id')) {
          // Navigate to wallpaper detail screen
          print('Navigate to wallpaper: ${data['wallpaper_id']}');
          // Note: Implement wallpaper detail navigation when screen is available
        }
        break;
      case 'category':
        // Navigate to specific category
        if (data.containsKey('category_name')) {
          // Navigate to category screen
          print('Navigate to category: ${data['category_name']}');
          // Note: Implement category navigation when screen is available
        }
        break;
      case 'home':
      default:
        // Navigate to home screen - ensure user stays on home screen
        print('Navigate to home screen');
        // Since the app already handles authentication state in AuthWrapper,
        // we don't need to navigate anywhere. The app will automatically
        // show the appropriate screen based on auth state.
        break;
    }
  }

  // Get FCM token
  static String? get fcmToken => _fcmToken;

  // Subscribe to topic
  static Future<void> subscribeToTopic(String topic) async {
    try {
      print('🔔 Attempting to subscribe to topic: $topic');
      await _firebaseMessaging.subscribeToTopic(topic);
      print('✅ Successfully subscribed to topic: $topic');

      // Verify subscription by checking token
      String? token = await _firebaseMessaging.getToken();
      print(
        '📱 FCM Token after subscription: ${token != null ? token.substring(0, 20) : 'null'}...',
      );
    } catch (e, stackTrace) {
      print('❌ Error subscribing to topic $topic: $e');
      print('Stack trace: $stackTrace');
      // Retry once after a delay
      await Future.delayed(const Duration(seconds: 2));
      try {
        await _firebaseMessaging.subscribeToTopic(topic);
        print('✅ Retry successful - Subscribed to topic: $topic');
      } catch (retryError) {
        print('❌ Retry failed for topic $topic: $retryError');
      }
      // Don't rethrow to prevent app crash
    }
  }

  // Show error snackbar
  static void _showErrorSnackBar(String message) {
    if (_currentContext != null) {
      ScaffoldMessenger.of(_currentContext!).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  // Unsubscribe from topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    try {
      print('🔕 Attempting to unsubscribe from topic: $topic');
      await _firebaseMessaging.unsubscribeFromTopic(topic);
      print('✅ Successfully unsubscribed from topic: $topic');
    } catch (e, stackTrace) {
      print('❌ Error unsubscribing from topic $topic: $e');
      print('Stack trace: $stackTrace');
      // Don't rethrow to prevent app crash
    }
  }

  // Save FCM token to user profile (optional)
  static Future<void> saveFCMTokenToProfile(String userId) async {
    if (_fcmToken != null) {
      try {
        // Save token to Firebase Realtime Database or Firestore
        // This allows sending targeted notifications to specific users
        print('Saving FCM token for user: $userId');
        // Implement your database save logic here
      } catch (e) {
        print('Error saving FCM token: $e');
      }
    }
  }

  // Save FCM token to Firestore
  static Future<void> _saveFCMTokenToFirestore(String token) async {
    try {
      final User? currentUser = _auth.currentUser;
      if (currentUser == null) {
        print('⚠️ No authenticated user, saving token as anonymous');
        // Save token for anonymous users too
        await _saveTokenForAnonymousUser(token);
        return;
      }

      final String userId = currentUser.uid;
      final String userEmail = currentUser.email ?? 'unknown';

      // Save token to user's document
      await _firestore.collection('users').doc(userId).set({
        'fcmToken': token,
        'email': userEmail,
        'lastTokenUpdate': FieldValue.serverTimestamp(),
        'deviceInfo': await _getDeviceInfo(),
      }, SetOptions(merge: true));

      // Also save to a separate tokens collection for easier querying
      await _firestore.collection('fcm_tokens').doc(userId).set({
        'token': token,
        'userId': userId,
        'email': userEmail,
        'lastUpdate': FieldValue.serverTimestamp(),
        'deviceInfo': await _getDeviceInfo(),
      });

      print('✅ FCM token saved to Firestore for user: $userEmail');
    } catch (e) {
      print('❌ Error saving FCM token to Firestore: $e');
    }
  }

  // Save token for anonymous users
  static Future<void> _saveTokenForAnonymousUser(String token) async {
    try {
      // Generate a unique ID for anonymous users
      final String anonymousId =
          'anonymous_${DateTime.now().millisecondsSinceEpoch}';

      await _firestore.collection('anonymous_tokens').add({
        'token': token,
        'anonymousId': anonymousId,
        'createdAt': FieldValue.serverTimestamp(),
        'deviceInfo': await _getDeviceInfo(),
      });

      print('✅ FCM token saved for anonymous user');
    } catch (e) {
      print('❌ Error saving anonymous FCM token: $e');
    }
  }

  // Get device information
  static Future<Map<String, dynamic>> _getDeviceInfo() async {
    try {
      final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        return {
          'platform': 'android',
          'model': androidInfo.model,
          'brand': androidInfo.brand,
          'version': androidInfo.version.release,
          'sdkInt': androidInfo.version.sdkInt,
        };
      } else if (Platform.isIOS) {
        final IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        return {
          'platform': 'ios',
          'model': iosInfo.model,
          'name': iosInfo.name,
          'systemVersion': iosInfo.systemVersion,
        };
      }

      return {'platform': 'unknown'};
    } catch (e) {
      print('❌ Error getting device info: $e');
      return {'platform': 'error'};
    }
  }

  // Public method to manually save current FCM token
  static Future<void> saveFCMTokenToFirestore() async {
    try {
      final String? token = await getFCMToken();
      if (token != null) {
        await _saveFCMTokenToFirestore(token);
      } else {
        print('❌ No FCM token available to save');
      }
    } catch (e) {
      print('❌ Error in saveFCMTokenToFirestore: $e');
    }
  }

  // Get all FCM tokens from Firestore (for admin use)
  static Future<List<String>> getAllFCMTokens() async {
    try {
      final List<String> tokens = [];

      // Get tokens from authenticated users
      final QuerySnapshot userTokens = await _firestore
          .collection('fcm_tokens')
          .get();
      for (final doc in userTokens.docs) {
        final data = doc.data() as Map<String, dynamic>;
        if (data['token'] != null) {
          tokens.add(data['token']);
        }
      }

      // Get tokens from anonymous users
      final QuerySnapshot anonymousTokens = await _firestore
          .collection('anonymous_tokens')
          .get();
      for (final doc in anonymousTokens.docs) {
        final data = doc.data() as Map<String, dynamic>;
        if (data['token'] != null) {
          tokens.add(data['token']);
        }
      }

      print('📱 Retrieved ${tokens.length} FCM tokens from Firestore');
      return tokens;
    } catch (e) {
      print('❌ Error getting FCM tokens from Firestore: $e');
      return [];
    }
  }

  // Send wallpaper upload notification to all users
  static Future<void> sendWallpaperUploadNotification({
    required String title,
    required String body,
    String? imageUrl,
    Map<String, dynamic>? data,
  }) async {
    try {
      print('📢 Sending wallpaper upload notification to all users...');

      // Save notification to Firestore for real-time delivery
      await _firestore.collection('live_notifications').add({
        'title': title,
        'body': body,
        'imageUrl': imageUrl,
        'data': data ?? {},
        'type': 'wallpaper_upload',
        'timestamp': FieldValue.serverTimestamp(),
        'source': 'wallpaper_upload',
        'targetTopic': 'all_users',
      });

      print('✅ Wallpaper upload notification saved to Firestore');
      print(
        '📱 Notification will be delivered to all users via Firestore listener',
      );
    } catch (e) {
      print('❌ Error sending wallpaper upload notification: $e');
    }
  }

  // Handle token refresh with retry mechanism
  static void setupTokenRefresh() {
    try {
      _firebaseMessaging.onTokenRefresh.listen(
        (String token) async {
          print('🔄 FCM Token refreshed: ${token.substring(0, 20)}...');
          _fcmToken = token;
          print('✅ Token updated in memory');

          // Save refreshed token to Firestore with retry
          await _saveFCMTokenWithRetry(token);

          // Re-subscribe to topics with new token
          await subscribeToTopic('all_users');
          print('✅ Re-subscribed to topics with new token');
        },
        onError: (error) {
          print('❌ Error in token refresh listener: $error');
        },
      );
      print('✅ Token refresh listener set up successfully');
    } catch (e) {
      print('❌ Error setting up token refresh listener: $e');
    }
  }

  // Save FCM token with retry mechanism
  static Future<void> _saveFCMTokenWithRetry(
    String token, {
    int maxRetries = 3,
  }) async {
    int retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        await _saveFCMTokenToFirestore(token);
        print('✅ FCM token saved successfully (attempt ${retryCount + 1})');
        return;
      } catch (e) {
        retryCount++;
        print('❌ Error saving FCM token (attempt $retryCount): $e');

        if (retryCount < maxRetries) {
          await Future.delayed(Duration(seconds: retryCount * 2));
        }
      }
    }

    print('❌ Failed to save FCM token after $maxRetries attempts');
  }

  // Periodic token refresh (call this periodically to ensure token is fresh)
  static Future<void> refreshTokenPeriodically() async {
    try {
      print('🔄 Performing periodic token refresh...');

      // Force get a new token
      await _firebaseMessaging.deleteToken();
      await Future.delayed(const Duration(seconds: 2));

      String? newToken = await _firebaseMessaging.getToken();
      if (newToken != null) {
        _fcmToken = newToken;
        await _saveFCMTokenWithRetry(newToken);
        await subscribeToTopic('all_users');
        print('✅ Periodic token refresh completed');
      } else {
        print('❌ Failed to get new token during periodic refresh');
      }
    } catch (e) {
      print('❌ Error during periodic token refresh: $e');
    }
  }

  // Schedule automatic token refresh every 7 days
  static void schedulePeriodicTokenRefresh() {
    Timer.periodic(const Duration(days: 7), (timer) async {
      print('⏰ Scheduled token refresh triggered');
      await refreshTokenPeriodically();
    });
    print('⏰ Scheduled periodic token refresh every 7 days');
  }

  // Check if app was killed and restart notification services
  static Future<void> restartNotificationServices() async {
    try {
      print('🔄 Restarting notification services...');

      // Re-initialize if needed
      if (_fcmToken == null) {
        await _getFCMTokenWithRetry();
      }

      // Re-subscribe to topics
      await subscribeToTopic('all_users');

      // Refresh token if it's old (older than 6 days)
      final prefs = await SharedPreferences.getInstance();
      final lastRefresh = prefs.getInt('last_token_refresh') ?? 0;
      final now = DateTime.now().millisecondsSinceEpoch;
      final daysSinceRefresh = (now - lastRefresh) / (1000 * 60 * 60 * 24);

      if (daysSinceRefresh > 6) {
        print(
          '🔄 Token is ${daysSinceRefresh.toStringAsFixed(1)} days old, refreshing...',
        );
        await refreshTokenPeriodically();
        await prefs.setInt('last_token_refresh', now);
      }

      print('✅ Notification services restarted successfully');
    } catch (e) {
      print('❌ Error restarting notification services: $e');
    }
  }
}
