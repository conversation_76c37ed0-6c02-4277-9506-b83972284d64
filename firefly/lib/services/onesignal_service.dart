import 'dart:io';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/onesignal_config.dart';

class OneSignalService {
  static bool _isInitialized = false;
  static String? _playerId;
  static String? _pushToken;

  /// Initialize OneSignal with your App ID
  static Future<void> initialize() async {
    try {
      print('🔔 Starting OneSignal initialization...');
      print('🔔 App ID: ${OneSignalConfig.appId}');

      // Validate configuration
      OneSignalConfig.validateConfiguration();
      print('✅ OneSignal configuration validated');

      // Enable debug logging - VERY IMPORTANT for troubleshooting
      OneSignal.Debug.setLogLevel(OSLogLevel.verbose);
      print('✅ OneSignal debug logging enabled');

      // OneSignal Initialization
      print('🔔 Calling OneSignal.initialize...');
      OneSignal.initialize(OneSignalConfig.appId);
      print('✅ OneSignal.initialize called');

      // Wait a moment for initialization
      await Future.delayed(const Duration(milliseconds: 500));

      // Set up notification handlers BEFORE requesting permission
      _setupNotificationHandlers();
      print('✅ Notification handlers set up');

      // Request permission for push notifications
      print('🔔 Requesting notification permission...');
      final permission = await OneSignal.Notifications.requestPermission(true);
      print('📱 Notification permission result: $permission');

      // Set external user ID if user is authenticated
      await _setExternalUserId();

      // Subscribe to tags and segments
      await _setupUserTags();

      // Wait a bit more and try to get user info
      await Future.delayed(const Duration(seconds: 2));
      await _logUserInfo();

      _isInitialized = true;
      print('✅ OneSignal initialization completed successfully');
    } catch (e, stackTrace) {
      print('❌ Error initializing OneSignal: $e');
      print('❌ Stack trace: $stackTrace');
    }
  }

  /// Log user information for debugging
  static Future<void> _logUserInfo() async {
    try {
      print('🔍 Getting OneSignal user info...');

      // Get OneSignal ID (may be null initially)
      final onesignalId = await OneSignal.User.getOnesignalId();
      print('🆔 OneSignal ID: $onesignalId');

      // Get push subscription info
      final pushSubscriptionId = OneSignal.User.pushSubscription.id;
      print('📱 Push Subscription ID: $pushSubscriptionId');

      final pushToken = OneSignal.User.pushSubscription.token;
      if (pushToken != null && pushToken.length > 20) {
        print('🔑 Push Token: ${pushToken.substring(0, 20)}...');
      } else {
        print('🔑 Push Token: $pushToken');
      }

      final optedIn = OneSignal.User.pushSubscription.optedIn;
      print('📱 Push Subscription Opted In: $optedIn');

      // Store the values
      _playerId = onesignalId ?? pushSubscriptionId;
      _pushToken = pushToken;

      // Save to local storage
      await _saveUserDataLocally();
    } catch (e) {
      print('❌ Error getting user info: $e');
    }
  }

  /// Set up notification event handlers
  static void _setupNotificationHandlers() {
    // Handle notification received while app is in foreground
    OneSignal.Notifications.addForegroundWillDisplayListener((event) {
      print(
        '🔔 Notification received in foreground: ${event.notification.title}',
      );

      // You can modify the notification or prevent it from showing
      // event.preventDefault(); // Prevents the notification from showing

      // Track analytics
      _trackNotificationReceived(event.notification);
    });

    // Handle notification clicked/opened
    OneSignal.Notifications.addClickListener((event) {
      print('🔔 Notification clicked: ${event.notification.title}');

      // Handle notification click action
      _handleNotificationClick(event.notification);

      // Track analytics
      _trackNotificationClicked(event.notification);
    });

    // Handle notification permission changes
    OneSignal.Notifications.addPermissionObserver((state) {
      print('🔔 Notification permission changed: ${state.toString()}');
    });
  }

  /// Set external user ID for better user tracking
  static Future<void> _setExternalUserId() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        OneSignal.login(user.uid);
        print('👤 External user ID set: ${user.uid}');
      }
    } catch (e) {
      print('❌ Error setting external user ID: $e');
    }
  }

  /// Set up user tags for better targeting
  static Future<void> _setupUserTags() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final user = FirebaseAuth.instance.currentUser;

      Map<String, String> tags = {
        OneSignalConfig.platformTag: Platform.isAndroid ? 'android' : 'ios',
        OneSignalConfig.appVersionTag:
            '1.0.10', // Update this with your app version
        OneSignalConfig.userTypeTag: user?.isAnonymous == true
            ? 'anonymous'
            : 'authenticated',
        OneSignalConfig.languageTag: Platform.localeName.split('_')[0],
      };

      // Add user preferences as tags
      if (prefs.getBool('notifications_enabled') == true) {
        tags[OneSignalConfig.notificationsEnabledTag] = 'true';
      }

      OneSignal.User.addTags(tags);
      print('🏷️ User tags set: $tags');
    } catch (e) {
      print('❌ Error setting user tags: $e');
    }
  }

  /// Handle notification click actions
  static void _handleNotificationClick(OSNotification notification) {
    try {
      // Get additional data from notification
      final additionalData = notification.additionalData;

      if (additionalData != null) {
        // Handle different notification types
        final notificationType = additionalData['type'];

        switch (notificationType) {
          case 'new_wallpaper':
            _handleNewWallpaperNotification(additionalData);
            break;
          case 'category_update':
            _handleCategoryUpdateNotification(additionalData);
            break;
          case 'app_update':
            _handleAppUpdateNotification(additionalData);
            break;
          default:
            print('🔔 Unknown notification type: $notificationType');
        }
      }
    } catch (e) {
      print('❌ Error handling notification click: $e');
    }
  }

  /// Handle new wallpaper notification
  static void _handleNewWallpaperNotification(Map<String, dynamic> data) {
    // Navigate to specific wallpaper or category
    final wallpaperId = data['wallpaper_id'];
    final category = data['category'];

    print('🖼️ New wallpaper notification: $wallpaperId in $category');
    // Add navigation logic here
  }

  /// Handle category update notification
  static void _handleCategoryUpdateNotification(Map<String, dynamic> data) {
    final category = data['category'];
    print('📂 Category update notification: $category');
    // Add navigation logic here
  }

  /// Handle app update notification
  static void _handleAppUpdateNotification(Map<String, dynamic> data) {
    final updateUrl = data['update_url'];
    print('🔄 App update notification: $updateUrl');
    // Add update logic here
  }

  /// Track notification received analytics
  static void _trackNotificationReceived(OSNotification notification) {
    try {
      // Use Firebase Analytics directly since AnalyticsService doesn't have a public logEvent method
      // You can create a custom method in AnalyticsService if needed
      print('📊 OneSignal notification received: ${notification.title}');
    } catch (e) {
      print('❌ Error tracking notification received: $e');
    }
  }

  /// Track notification clicked analytics
  static void _trackNotificationClicked(OSNotification notification) {
    try {
      // Use Firebase Analytics directly since AnalyticsService doesn't have a public logEvent method
      // You can create a custom method in AnalyticsService if needed
      print('📊 OneSignal notification clicked: ${notification.title}');
    } catch (e) {
      print('❌ Error tracking notification clicked: $e');
    }
  }

  /// Save user data to local storage
  static Future<void> _saveUserDataLocally() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (_playerId != null) {
        await prefs.setString('onesignal_player_id', _playerId!);
      }
      if (_pushToken != null) {
        await prefs.setString('onesignal_push_token', _pushToken!);
      }
    } catch (e) {
      print('❌ Error saving user data locally: $e');
    }
  }

  /// Sync OneSignal data with Firebase
  static Future<void> _syncWithFirebase() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null || _playerId == null) return;

      await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .update({
            'onesignal_player_id': _playerId,
            'onesignal_push_token': _pushToken,
            'last_onesignal_sync': FieldValue.serverTimestamp(),
          });

      print('✅ OneSignal data synced with Firebase');
    } catch (e) {
      print('❌ Error syncing with Firebase: $e');
    }
  }

  /// Send a tag to OneSignal for user segmentation
  static Future<void> sendTag(String key, String value) async {
    try {
      OneSignal.User.addTags({key: value});
      print('🏷️ Tag sent: $key = $value');
    } catch (e) {
      print('❌ Error sending tag: $e');
    }
  }

  /// Remove a tag from OneSignal
  static Future<void> removeTag(String key) async {
    try {
      OneSignal.User.removeTags([key]);
      print('🗑️ Tag removed: $key');
    } catch (e) {
      print('❌ Error removing tag: $e');
    }
  }

  /// Get current player ID
  static String? get playerId => _playerId;

  /// Get current push token
  static String? get pushToken => _pushToken;

  /// Check if OneSignal is initialized
  static bool get isInitialized => _isInitialized;

  /// Logout user from OneSignal
  static Future<void> logout() async {
    try {
      OneSignal.logout();
      _playerId = null;
      _pushToken = null;
      print('👋 User logged out from OneSignal');
    } catch (e) {
      print('❌ Error logging out from OneSignal: $e');
    }
  }

  /// Request notification permission
  static Future<bool> requestPermission() async {
    try {
      final permission = await OneSignal.Notifications.requestPermission(true);
      print('🔔 Notification permission: $permission');
      return permission;
    } catch (e) {
      print('❌ Error requesting permission: $e');
      return false;
    }
  }
}
