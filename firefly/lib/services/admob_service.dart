import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'premium_access_service.dart';
import 'admob_config_service.dart';

class AdMobService {
  static final AdMobService _instance = AdMobService._internal();
  factory AdMobService() => _instance;
  AdMobService._internal();

  // Dynamic Ad Unit IDs from admin configuration
  static Future<String> get _bannerAdUnitId async => Platform.isAndroid
      ? await AdMobConfigService.getAndroidBannerId()
      : await AdMobConfigService.getIosBannerId();

  static Future<String> get _interstitialAdUnitId async => Platform.isAndroid
      ? await AdMobConfigService.getAndroidInterstitialId()
      : await AdMobConfigService.getIosInterstitialId();

  static Future<String> get _rewardedAdUnitId async => Platform.isAndroid
      ? await AdMobConfigService.getAndroidRewardedId()
      : await AdMobConfigService.getIosRewardedId();

  // Ad instances
  BannerAd? _bannerAd;
  InterstitialAd? _interstitialAd;
  RewardedAd? _rewardedAd;

  // Ad loading states
  bool _isBannerAdLoaded = false;
  bool _isInterstitialAdLoaded = false;
  bool _isRewardedAdLoaded = false;

  // Ad frequency control
  static const String _lastInterstitialKey = 'last_interstitial_time';
  static const String _interstitialCountKey = 'interstitial_count_today';
  static const String _lastDateKey = 'last_date';
  static const int _interstitialCooldownMinutes =
      3; // 3 minutes between interstitials
  static const int _maxInterstitialsPerDay = 10; // Max 10 interstitials per day

  // Getters
  bool get isBannerAdLoaded => _isBannerAdLoaded;
  bool get isInterstitialAdLoaded => _isInterstitialAdLoaded;
  bool get isRewardedAdLoaded => _isRewardedAdLoaded;
  BannerAd? get bannerAd => _bannerAd;
  RewardedAd? get rewardedAd => _rewardedAd;

  /// Initialize AdMob
  Future<void> initialize() async {
    try {
      await MobileAds.instance.initialize();

      // Set request configuration for better ad targeting
      final RequestConfiguration requestConfiguration = RequestConfiguration(
        testDeviceIds: kDebugMode ? ['YOUR_TEST_DEVICE_ID'] : [],
        tagForChildDirectedTreatment: TagForChildDirectedTreatment.no,
        tagForUnderAgeOfConsent: TagForUnderAgeOfConsent.no,
      );

      MobileAds.instance.updateRequestConfiguration(requestConfiguration);

      // Pre-load ads
      await loadBannerAd();
      await loadInterstitialAd();
      await loadRewardedAd();

      if (kDebugMode) {
        print('🎯 AdMob initialized successfully');
        print('🎯 Banner ad loading: ${_isBannerAdLoaded}');
        print('🎯 Interstitial ad loading: ${_isInterstitialAdLoaded}');
        print('🎯 Rewarded ad loading: ${_isRewardedAdLoaded}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to initialize AdMob: $e');
      }
    }
  }

  /// Load banner ad
  Future<void> loadBannerAd() async {
    final adUnitId = await _bannerAdUnitId;
    _bannerAd = BannerAd(
      adUnitId: adUnitId,
      size: AdSize.banner,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          _isBannerAdLoaded = true;
          if (kDebugMode) {
            print('🎯 Banner ad loaded successfully');
          }
        },
        onAdFailedToLoad: (ad, error) {
          _isBannerAdLoaded = false;
          ad.dispose();
          if (kDebugMode) {
            print('Banner ad failed to load: $error');
          }
          // Retry loading after 30 seconds
          Future.delayed(const Duration(seconds: 30), () {
            loadBannerAd();
          });
        },
        onAdOpened: (ad) {
          if (kDebugMode) {
            print('Banner ad opened');
          }
        },
        onAdClosed: (ad) {
          if (kDebugMode) {
            print('Banner ad closed');
          }
        },
      ),
    );
    _bannerAd!.load();
  }

  /// Load interstitial ad
  Future<void> loadInterstitialAd() async {
    final adUnitId = await _interstitialAdUnitId;
    InterstitialAd.load(
      adUnitId: adUnitId,
      request: const AdRequest(),
      adLoadCallback: InterstitialAdLoadCallback(
        onAdLoaded: (ad) {
          _interstitialAd = ad;
          _isInterstitialAdLoaded = true;
          if (kDebugMode) {
            print('Interstitial ad loaded');
          }

          // Set full screen content callback
          _interstitialAd!.setImmersiveMode(true);
          _interstitialAd!.fullScreenContentCallback =
              FullScreenContentCallback(
                onAdShowedFullScreenContent: (ad) {
                  if (kDebugMode) {
                    print('Interstitial ad showed full screen content');
                  }
                },
                onAdDismissedFullScreenContent: (ad) {
                  ad.dispose();
                  _isInterstitialAdLoaded = false;
                  if (kDebugMode) {
                    print('Interstitial ad dismissed');
                  }
                  // Load next interstitial
                  loadInterstitialAd();
                },
                onAdFailedToShowFullScreenContent: (ad, error) {
                  ad.dispose();
                  _isInterstitialAdLoaded = false;
                  if (kDebugMode) {
                    print('Interstitial ad failed to show: $error');
                  }
                  // Load next interstitial
                  loadInterstitialAd();
                },
              );
        },
        onAdFailedToLoad: (error) {
          _isInterstitialAdLoaded = false;
          if (kDebugMode) {
            print('Interstitial ad failed to load: $error');
          }
          // Retry loading after 30 seconds
          Future.delayed(const Duration(seconds: 30), () {
            loadInterstitialAd();
          });
        },
      ),
    );
  }

  /// Load rewarded ad
  Future<void> loadRewardedAd() async {
    final adUnitId = await _rewardedAdUnitId;
    RewardedAd.load(
      adUnitId: adUnitId,
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (ad) {
          _rewardedAd = ad;
          _isRewardedAdLoaded = true;
          if (kDebugMode) {
            print('🎯 Rewarded ad loaded successfully');
          }

          // Set full screen content callback
          _rewardedAd!.setImmersiveMode(true);
          _rewardedAd!.fullScreenContentCallback = FullScreenContentCallback(
            onAdShowedFullScreenContent: (ad) {
              if (kDebugMode) {
                print('Rewarded ad showed full screen content');
              }
            },
            onAdDismissedFullScreenContent: (ad) {
              ad.dispose();
              _isRewardedAdLoaded = false;
              if (kDebugMode) {
                print('Rewarded ad dismissed');
              }
              // Load next rewarded ad
              loadRewardedAd();
            },
            onAdFailedToShowFullScreenContent: (ad, error) {
              ad.dispose();
              _isRewardedAdLoaded = false;
              if (kDebugMode) {
                print('Rewarded ad failed to show: $error');
              }
              // Load next rewarded ad
              loadRewardedAd();
            },
          );
        },
        onAdFailedToLoad: (error) {
          _isRewardedAdLoaded = false;
          if (kDebugMode) {
            print('🎯 Rewarded ad failed to load: $error');
            print('🎯 Error code: ${error.code}');
            print('🎯 Error domain: ${error.domain}');
            print('🎯 Error message: ${error.message}');
          }
          // Retry loading after 30 seconds
          Future.delayed(const Duration(seconds: 30), () {
            if (kDebugMode) {
              print('🎯 Retrying rewarded ad load...');
            }
            loadRewardedAd();
          });
        },
      ),
    );
  }

  /// Show interstitial ad with frequency control
  Future<bool> showInterstitialAd() async {
    // Check if ads are enabled globally
    final adsEnabled = await AdMobConfigService.areAdsEnabled();
    if (!adsEnabled) {
      return false; // Don't show ads if disabled by admin
    }

    // Check if user has ad-free access
    final hasAdFreeAccess = await PremiumAccessService.hasAdFreeAccess();
    if (hasAdFreeAccess) {
      return false; // Don't show ads if user has ad-free access
    }

    if (!_isInterstitialAdLoaded || _interstitialAd == null) {
      return false;
    }

    // Check frequency limits
    if (!(await _canShowInterstitial())) {
      return false;
    }

    try {
      await _interstitialAd!.show();
      await _recordInterstitialShown();
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error showing interstitial ad: $e');
      }
      return false;
    }
  }

  /// Show rewarded ad
  Future<bool> showRewardedAd({
    required OnUserEarnedRewardCallback onUserEarnedReward,
  }) async {
    if (!_isRewardedAdLoaded || _rewardedAd == null) {
      return false;
    }

    try {
      await _rewardedAd!.show(onUserEarnedReward: onUserEarnedReward);
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error showing rewarded ad: $e');
      }
      return false;
    }
  }

  /// Check if interstitial ad can be shown based on frequency limits
  Future<bool> _canShowInterstitial() async {
    final prefs = await SharedPreferences.getInstance();
    final now = DateTime.now();

    // Check cooldown period
    final lastShown = prefs.getInt(_lastInterstitialKey) ?? 0;
    final timeSinceLastAd = now.millisecondsSinceEpoch - lastShown;
    if (timeSinceLastAd < _interstitialCooldownMinutes * 60 * 1000) {
      return false;
    }

    // Check daily limit
    final today = DateTime(now.year, now.month, now.day).millisecondsSinceEpoch;
    final lastDate = prefs.getInt(_lastDateKey) ?? 0;

    if (lastDate == today) {
      final todayCount = prefs.getInt(_interstitialCountKey) ?? 0;
      if (todayCount >= _maxInterstitialsPerDay) {
        return false;
      }
    }

    return true;
  }

  /// Record that an interstitial ad was shown
  Future<void> _recordInterstitialShown() async {
    final prefs = await SharedPreferences.getInstance();
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day).millisecondsSinceEpoch;

    await prefs.setInt(_lastInterstitialKey, now.millisecondsSinceEpoch);

    final lastDate = prefs.getInt(_lastDateKey) ?? 0;
    if (lastDate == today) {
      final todayCount = prefs.getInt(_interstitialCountKey) ?? 0;
      await prefs.setInt(_interstitialCountKey, todayCount + 1);
    } else {
      await prefs.setInt(_lastDateKey, today);
      await prefs.setInt(_interstitialCountKey, 1);
    }
  }

  /// Dispose all ads
  void dispose() {
    _bannerAd?.dispose();
    _interstitialAd?.dispose();
    _rewardedAd?.dispose();
    _isBannerAdLoaded = false;
    _isInterstitialAdLoaded = false;
    _isRewardedAdLoaded = false;
  }
}
