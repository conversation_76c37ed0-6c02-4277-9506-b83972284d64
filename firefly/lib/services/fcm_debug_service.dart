import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io';

class FCMDebugService {
  static final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  
  /// Basic FCM diagnostic check for debug builds only
  static Future<void> runComprehensiveDiagnostics() async {
    if (!kDebugMode) return; // Only run in debug mode
    
    print('\n=== 🔍 FCM BASIC DIAGNOSTICS START ===');
    
    try {
      // Basic checks only
      await _checkFirebaseInitialization();
      await _checkFCMToken();
      
      print('\n=== ✅ FCM BASIC DIAGNOSTICS COMPLETE ===\n');
      
    } catch (e) {
      print('❌ Error during diagnostics: $e');
    }
  }
  
  static Future<void> _checkFirebaseInitialization() async {
    print('\n🔥 Checking Firebase initialization...');
    try {
      final app = Firebase.app();
      print('✅ Firebase app initialized: ${app.name}');
      print('✅ Firebase options: ${app.options.projectId}');
    } catch (e) {
      print('❌ Firebase not initialized: $e');
    }
  }
  
  static Future<void> _checkDeviceInfo() async {
    print('\n📱 Checking device information...');
    try {
      final deviceInfo = DeviceInfoPlugin();
      
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        print('✅ Android version: ${androidInfo.version.release}');
        print('✅ SDK version: ${androidInfo.version.sdkInt}');
        print('✅ Device model: ${androidInfo.model}');
        print('✅ Manufacturer: ${androidInfo.manufacturer}');
        
        // Check if Android 13+ for notification permissions
        if (androidInfo.version.sdkInt >= 33) {
          print('⚠️ Android 13+ detected - Runtime notification permission required');
        }
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        print('✅ iOS version: ${iosInfo.systemVersion}');
        print('✅ Device model: ${iosInfo.model}');
      }
    } catch (e) {
      print('❌ Error getting device info: $e');
    }
  }
  
  static Future<void> _checkNotificationPermissions() async {
    print('\n🔔 Checking notification permissions...');
    try {
      final settings = await _firebaseMessaging.getNotificationSettings();
      
      print('📱 Authorization status: ${settings.authorizationStatus}');
      print('📱 Alert setting: ${settings.alert}');
      print('📱 Badge setting: ${settings.badge}');
      print('📱 Sound setting: ${settings.sound}');
      print('📱 Announcement setting: ${settings.announcement}');
      print('📱 Car Play setting: ${settings.carPlay}');
      print('📱 Critical Alert setting: ${settings.criticalAlert}');
      print('📱 Provisional setting: ${settings.authorizationStatus == AuthorizationStatus.provisional}');
      print('📱 Time Sensitive setting: ${settings.timeSensitive}');
      
      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        print('✅ Notifications are fully authorized');
      } else if (settings.authorizationStatus == AuthorizationStatus.provisional) {
        print('⚠️ Notifications are provisionally authorized');
      } else {
        print('❌ Notifications are not authorized');
        print('💡 User needs to enable notifications in device settings');
      }
    } catch (e) {
      print('❌ Error checking permissions: $e');
    }
  }
  
  static Future<void> _checkFCMToken() async {
    print('\n🎫 Checking FCM token...');
    try {
      final token = await _firebaseMessaging.getToken();
      if (token != null) {
        print('✅ FCM Token obtained: ${token.substring(0, 20)}...');
        print('📝 Full token length: ${token.length} characters');
        
        // Validate token format
        if (token.length > 100 && token.contains(':')) {
          print('✅ Token format appears valid');
        } else {
          print('⚠️ Token format may be invalid');
        }
      } else {
        print('❌ FCM Token is null');
        print('💡 This usually indicates a Firebase configuration issue');
      }
    } catch (e) {
      print('❌ Error getting FCM token: $e');
    }
  }
  
  static Future<void> _checkTopicSubscriptions() async {
    print('\n📢 Checking topic subscriptions...');
    try {
      // Test subscribing to a topic
      await _firebaseMessaging.subscribeToTopic('debug_test');
      print('✅ Successfully subscribed to debug_test topic');
      
      // Unsubscribe immediately
      await _firebaseMessaging.unsubscribeFromTopic('debug_test');
      print('✅ Successfully unsubscribed from debug_test topic');
      
      print('💡 Topic subscription mechanism is working');
    } catch (e) {
      print('❌ Error with topic subscriptions: $e');
    }
  }
  
  static Future<void> _checkMessageHandlers() async {
    print('\n📨 Checking message handlers...');
    
    // Check if handlers are set up
    print('✅ Foreground message handler: Available');
    print('✅ Background message handler: Available');
    print('✅ Terminated app message handler: Available');
    
    // Test getting initial message
    try {
      final initialMessage = await _firebaseMessaging.getInitialMessage();
      if (initialMessage != null) {
        print('📨 Initial message found: ${initialMessage.messageId}');
      } else {
        print('📨 No initial message (app not opened from notification)');
      }
    } catch (e) {
      print('❌ Error getting initial message: $e');
    }
  }
  
  static Future<void> _testLocalNotifications() async {
    print('\n🔔 Testing local notifications...');
    try {
      // This would require importing the NotificationService
      print('💡 Local notification test should be implemented');
      print('💡 Check if flutter_local_notifications is working');
    } catch (e) {
      print('❌ Error testing local notifications: $e');
    }
  }
  
  static void _checkProGuardConfiguration() {
    print('\n🛡️ Checking ProGuard configuration...');
    
    if (kReleaseMode) {
      print('⚠️ Running in RELEASE mode');
      print('💡 Ensure ProGuard rules include:');
      print('   -keep class com.google.firebase.** { *; }');
      print('   -keep class com.google.firebase.messaging.** { *; }');
      print('   -keepclassmembers class * extends com.google.firebase.messaging.FirebaseMessagingService { *; }');
    } else {
      print('✅ Running in DEBUG mode - ProGuard not applied');
    }
  }
  
  static void _checkBuildConfiguration() {
    print('\n🏗️ Checking build configuration...');
    
    print('📱 Platform: ${Platform.operatingSystem}');
    print('🔧 Debug mode: $kDebugMode');
    print('🔧 Profile mode: $kProfileMode');
    print('🔧 Release mode: $kReleaseMode');
    
    if (kReleaseMode) {
      print('⚠️ RELEASE BUILD DETECTED');
      print('💡 Common release build issues:');
      print('   1. ProGuard obfuscation affecting Firebase classes');
      print('   2. Different SHA-1 fingerprints (Play Store vs local)');
      print('   3. Missing google-services.json or incorrect configuration');
      print('   4. Network security config blocking Firebase connections');
    }
  }
  
  /// Test sending a notification to current device
  static Future<void> testNotificationToCurrentDevice() async {
    print('\n🧪 Testing notification to current device...');
    
    try {
      final token = await _firebaseMessaging.getToken();
      if (token != null) {
        print('✅ Device token: ${token.substring(0, 20)}...');
        print('💡 Use this token to send test notifications from Firebase Console');
        print('💡 Firebase Console > Cloud Messaging > Send test message');
      } else {
        print('❌ Cannot test - no FCM token available');
      }
    } catch (e) {
      print('❌ Error preparing notification test: $e');
    }
  }
  
  /// Generate a comprehensive diagnostic report
  static Future<Map<String, dynamic>> generateDiagnosticReport() async {
    final report = <String, dynamic>{};
    
    try {
      // Firebase status
      try {
        final app = Firebase.app();
        report['firebase_initialized'] = true;
        report['firebase_project_id'] = app.options.projectId;
      } catch (e) {
        report['firebase_initialized'] = false;
        report['firebase_error'] = e.toString();
      }
      
      // FCM token
      try {
        final token = await _firebaseMessaging.getToken();
        report['fcm_token_available'] = token != null;
        report['fcm_token_length'] = token?.length ?? 0;
      } catch (e) {
        report['fcm_token_available'] = false;
        report['fcm_token_error'] = e.toString();
      }
      
      // Permissions
      try {
        final settings = await _firebaseMessaging.getNotificationSettings();
        report['notification_permission'] = settings.authorizationStatus.toString();
        report['alert_permission'] = settings.alert.toString();
        report['badge_permission'] = settings.badge.toString();
        report['sound_permission'] = settings.sound.toString();
      } catch (e) {
        report['permission_error'] = e.toString();
      }
      
      // Build info
      report['debug_mode'] = kDebugMode;
      report['profile_mode'] = kProfileMode;
      report['release_mode'] = kReleaseMode;
      report['platform'] = Platform.operatingSystem;
      
      // Device info
      try {
        final deviceInfo = DeviceInfoPlugin();
        if (Platform.isAndroid) {
          final androidInfo = await deviceInfo.androidInfo;
          report['android_version'] = androidInfo.version.release;
          report['android_sdk'] = androidInfo.version.sdkInt;
          report['device_model'] = androidInfo.model;
        }
      } catch (e) {
        report['device_info_error'] = e.toString();
      }
      
    } catch (e) {
      report['report_generation_error'] = e.toString();
    }
    
    return report;
  }
}