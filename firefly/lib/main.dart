import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:provider/provider.dart';
import 'package:flutter/foundation.dart';
import 'screens/splash_screen.dart';
import 'screens/onboarding_screen.dart';
import 'screens/home_screen.dart';
import 'providers/auth_provider.dart';
import 'providers/wallpaper_provider.dart';
import 'providers/admin_config_provider.dart';
import 'utils/app_theme.dart';
import 'services/notification_listener_service.dart';
import 'services/fcm_debug_service.dart';

// Import the notification service
import 'services/notification_service.dart';
// Import the background handler
import 'services/firebase_background_handler.dart';
// Import analytics service
import 'services/analytics_service.dart';
// Import battery optimization service
import 'services/battery_optimization_service.dart';
// Import OneSignal service
import 'services/onesignal_service.dart';
// Import rating prompt service
import 'services/rating_prompt_service.dart';
// Import AdMob service
import 'services/admob_service.dart';
import 'services/admob_config_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Only initialize absolutely critical services before app starts
  try {
    await Firebase.initializeApp();
    print('✅ Firebase initialized');
  } catch (e) {
    print('❌ Firebase initialization failed: $e');
  }

  runApp(const MyApp());

  // Initialize all other services after app UI starts
  initializeServices();
}

// Initialize additional services after app starts
Future<void> initializeServices() async {
  // Initialize services in parallel for faster startup
  final futures = <Future<void>>[];

  // Add AdMob initialization
  futures.add(
    AdMobConfigService.setAdsEnabled(
      true,
    ).then((_) => AdMobService().initialize()).catchError((e) {
      if (kDebugMode) print('❌ AdMob service failed: $e');
    }),
  );

  // Add background message handler
  futures.add(
    Future(() {
      FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
      if (kDebugMode) print('✅ Background message handler registered');
    }).catchError((e) {
      if (kDebugMode)
        print('❌ Error registering background message handler: $e');
    }),
  );

  // Add analytics service
  futures.add(
    AnalyticsService.initialize().catchError((e) {
      if (kDebugMode) print('❌ Analytics service failed: $e');
    }),
  );

  // Add notification service
  futures.add(
    NotificationService.initialize().catchError((e) {
      if (kDebugMode) print('❌ Notification service failed: $e');
    }),
  );

  // Add rating prompt service
  futures.add(
    RatingPromptService.initialize().catchError((e) {
      if (kDebugMode) print('❌ Rating prompt service failed: $e');
    }),
  );

  // Add OneSignal service
  futures.add(
    OneSignalService.initialize().catchError((e) {
      if (kDebugMode) print('❌ OneSignal service failed: $e');
    }),
  );

  // Wait for all services to initialize in parallel
  try {
    await Future.wait(futures);
    if (kDebugMode) print('✅ All services initialized');
  } catch (e) {
    if (kDebugMode) print('❌ Some services failed to initialize: $e');
  }

  // Initialize additional services that need to run after core services
  try {
    // Initialize notification listener service with delay
    await Future.delayed(const Duration(seconds: 1));
    await NotificationListenerService.initialize();
    if (kDebugMode) print('✅ Notification listener service initialized');

    // Run FCM diagnostics in debug mode
    if (kDebugMode) {
      await FCMDebugService.runComprehensiveDiagnostics();
    }

    if (kDebugMode) print('🎉 All services initialized successfully');
  } catch (e) {
    if (kDebugMode) print('❌ Service initialization error: $e');
    // Continue app execution even if some services fail
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AdminConfigProvider()),
        ChangeNotifierProxyProvider<AdminConfigProvider, AuthProvider>(
          create: (context) =>
              AuthProvider(context.read<AdminConfigProvider>()),
          update: (context, adminConfig, previous) =>
              previous ?? AuthProvider(adminConfig),
        ),
        ChangeNotifierProxyProvider<AdminConfigProvider, WallpaperProvider>(
          create: (context) =>
              WallpaperProvider(context.read<AdminConfigProvider>()),
          update: (context, adminConfig, previous) =>
              previous ?? WallpaperProvider(adminConfig),
        ),
      ],
      child: MaterialApp(
        title: 'FireFly - 4K HD Wallpaper',
        theme: AppTheme.lightTheme,
        home: const AuthWrapper(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _showOnboarding = true;
  bool _isCheckingOnboarding = true;
  bool _isTransitioning = false;

  @override
  void initState() {
    super.initState();
    _checkOnboardingStatus();
  }

  // Check if user has completed onboarding before
  Future<void> _checkOnboardingStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hasCompletedOnboarding =
          prefs.getBool('has_completed_onboarding') ?? false;

      // Add minimum delay to prevent flashing
      await Future.delayed(const Duration(milliseconds: 300));

      if (mounted) {
        setState(() {
          _showOnboarding = !hasCompletedOnboarding;
          _isCheckingOnboarding = false;
        });
      }
    } catch (e) {
      print('❌ Error checking onboarding status: $e');
      if (mounted) {
        setState(() {
          _showOnboarding = true; // Default to showing onboarding on error
          _isCheckingOnboarding = false;
        });
      }
    }
  }

  // This will be called when user clicks "Get Started"
  Future<void> _initializeAppServices() async {
    try {
      // Initialize services without delay for faster startup
      initializeServices(); // Don't await - let it run in background

      // Track app open event
      try {
        AnalyticsService.trackAppOpen(); // Don't await - let it run in background
      } catch (e) {
        if (kDebugMode) print('❌ Failed to track app open: $e');
      }

      // Check battery optimization after a delay (don't block app startup)
      Future.delayed(const Duration(seconds: 3), () async {
        if (mounted) {
          try {
            bool isBatteryOptimized =
                !(await BatteryOptimizationService.isBatteryOptimizationDisabled());
            if (isBatteryOptimized) {
              print(
                '⚠️ Battery optimization is enabled - notifications may be unreliable',
              );
              // Show dialog after another delay to not interrupt user experience
              Future.delayed(const Duration(seconds: 5), () {
                if (mounted) {
                  BatteryOptimizationService.showBatteryOptimizationDialog(
                    context,
                  );
                }
              });
            }
          } catch (e) {
            print('❌ Error checking battery optimization: $e');
          }
        }
      });
    } catch (e) {
      print('Failed to initialize services: $e');
    }
  }

  Future<void> _onGetStarted() async {
    // Prevent multiple taps
    if (_isTransitioning) return;

    setState(() {
      _isTransitioning = true;
    });

    // Save onboarding completion status
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('has_completed_onboarding', true);
      print('✅ Onboarding completion saved to SharedPreferences');
    } catch (e) {
      print('❌ Error saving onboarding status: $e');
    }

    // Add a small delay for better UX (let user see the button press)
    await Future.delayed(const Duration(milliseconds: 500));

    if (mounted) {
      setState(() {
        _showOnboarding = false;
        _isTransitioning = false;
      });
      // Initialize services in background after user proceeds
      _initializeAppServices();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Show splash while checking onboarding status
    if (_isCheckingOnboarding) {
      return const SplashScreen();
    }

    // Show onboarding screen if user hasn't completed it
    if (_showOnboarding) {
      return OnboardingScreen(onGetStarted: _onGetStarted);
    }

    // After onboarding, show splash while services initialize, then home
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        print(
          '🔍🔍🔍 AUTH WRAPPER REBUILD: isLoading = ${authProvider.isLoading}, isLoggedIn = ${authProvider.isLoggedIn}, isAnonymous = ${authProvider.isAnonymous}, user = ${authProvider.user?.uid ?? "null"}',
        );

        // Show splash screen while loading or during initial auth state check
        if (authProvider.isLoading) {
          print('🔍 SHOWING: SplashScreen (loading)');
          return const SplashScreen();
        }

        // After services are initialized, go to home screen
        print('🔍 SHOWING: HomeScreen');
        return const HomeScreen();
      },
    );
  }
}
