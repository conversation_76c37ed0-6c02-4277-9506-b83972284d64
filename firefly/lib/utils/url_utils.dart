/// Utility class for handling URL operations
class UrlUtils {
  /// Fixes duplicate folder paths in URLs automatically for any category
  /// 
  /// This function uses regex to detect and fix patterns like:
  /// - /nature/nature/ → /nature/
  /// - /god/god/ → /god/
  /// - /abstract/abstract/ → /abstract/
  /// 
  /// It works for any category name automatically, so you don't need to
  /// manually add fixes for new categories.
  /// 
  /// Example:
  /// ```dart
  /// String fixedUrl = UrlUtils.fixDuplicateFolderPaths(originalUrl);
  /// ```
  static String fixDuplicateFolderPaths(String url) {
    if (url.isEmpty) return url;
    
    String fixedUrl = url;
    
    // Use regex to find and fix any duplicate folder patterns like /category/category/
    // Pattern explanation:
    // - / matches the forward slash
    // - ([^/]+) captures any characters except forward slash (the category name)
    // - / matches another forward slash
    // - \1 matches the same text as the first captured group (duplicate category name)
    // - / matches the final forward slash
    final duplicatePattern = RegExp(r'/([^/]+)/\1/');
    
    // Keep fixing until no more duplicates are found
    // This handles cases where there might be multiple duplications
    while (duplicatePattern.hasMatch(fixedUrl)) {
      fixedUrl = fixedUrl.replaceAllMapped(duplicatePattern, (match) {
        final categoryName = match.group(1);
        return '/$categoryName/';
      });
    }
    
    return fixedUrl;
  }
  
  /// Validates if a URL is properly formatted
  static bool isValidUrl(String url) {
    if (url.isEmpty) return false;
    
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }
  
  /// Gets the file extension from a URL
  static String getFileExtension(String url) {
    try {
      final uri = Uri.parse(url);
      final path = uri.path;
      final lastDotIndex = path.lastIndexOf('.');
      
      if (lastDotIndex != -1 && lastDotIndex < path.length - 1) {
        return path.substring(lastDotIndex + 1).toLowerCase();
      }
      
      return '';
    } catch (e) {
      return '';
    }
  }
  
  /// Checks if URL points to an image file
  static bool isImageUrl(String url) {
    final extension = getFileExtension(url);
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];
    return imageExtensions.contains(extension);
  }
}
