rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read access to wallpapers for all users
    match /wallpapers/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Allow users to read/write their own user documents and subcollections
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;

      // Allow access to user subcollections (preferences, settings, etc.)
      match /{document=**} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
    }
    
    // Allow authenticated users (including anonymous) to save their FCM tokens
    match /fcm_tokens/{document=**} {
      allow read, write: if request.auth != null;
    }

    // Allow anonymous FCM tokens (for users not logged in)
    match /anonymous_tokens/{document} {
      allow read, write: if true;
    }

    // Allow analytics events - temporarily allow all operations for testing
    match /analytics_events/{document} {
      allow read, write: if true;
    }

    // Allow user activity tracking for authenticated users
    match /user_activity/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Allow user stats tracking for authenticated users
    match /user_stats/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;

      // Allow access to user stats subcollections
      match /{document=**} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
    }

    // Allow general analytics and tracking for authenticated users
    match /analytics/{document=**} {
      allow read, write: if request.auth != null;
    }

    // Allow app usage tracking
    match /app_usage/{document=**} {
      allow read, write: if request.auth != null;
    }
    
    // Allow reading live notifications for all users
    match /live_notifications/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Allow reading notification history for all users
    match /notification_history/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Admin-only collections (you can customize admin check logic)
    match /admin_config/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Categories collection
    match /categories/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    // App ratings collection - temporarily allow all operations for testing
    // TODO: Tighten security after testing
    match /app_ratings/{document} {
      allow read, write: if true;
    }

    // Default rule - deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}